package com.enosisbd.app.service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.enosisbd.app.config.FileWatcherProperties;
import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.model.ScrapeRequestBatch;
import com.enosisbd.app.model.ScrapeStatus;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileWatcherService {
    
    private final FileWatcherProperties fileWatcherProperties;
    private final FileBasedJobService fileBasedJobService;
    private final ObjectMapper objectMapper;
    
    private final Set<String> processingFiles = Collections.synchronizedSet(new HashSet<>());
    private Semaphore processingLimiter;
    
    @PostConstruct
    public void init() {
        if (fileWatcherProperties.isEnabled()) {
            log.info("File Watcher Service initialized");
            this.processingLimiter = new Semaphore(fileWatcherProperties.getMaxConcurrentFiles());
            createDirectoriesIfNeeded();
            setupObjectMapper();
        } else {
            log.info("File Watcher Service is disabled");
        }
    }
    
    private void setupObjectMapper() {
        objectMapper.registerModule(new JavaTimeModule());
    }
    
    private void createDirectoriesIfNeeded() {
        if (fileWatcherProperties.isAutoCreateFolders()) {
            createDirectory(fileWatcherProperties.getInputFolder());
            createDirectory(fileWatcherProperties.getOutputFolder());
            createDirectory(fileWatcherProperties.getProcessedFolder());
            createDirectory(fileWatcherProperties.getErrorFolder());
        }
    }
    
    private void createDirectory(String path) {
        try {
            Path directory = Paths.get(path);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
                log.info("Created directory: {}", path);
            }
        } catch (IOException e) {
            log.error("Failed to create directory: {}", path, e);
        }
    }
    
    @Scheduled(fixedDelayString = "#{${file-watcher.polling-interval-seconds} * 1000}")
    public void watchInputFolder() {
        if (!fileWatcherProperties.isEnabled()) {
            return;
        }
        
        try {
            Path inputPath = Paths.get(fileWatcherProperties.getInputFolder());
            if (!Files.exists(inputPath)) {
                return;
            }
            
            Files.list(inputPath)
                    .filter(Files::isRegularFile)
                    .filter(this::isSupportedFile)
                    .filter(path -> !processingFiles.contains(path.toString()))
                    .forEach(this::processFileAsync);
                    
        } catch (IOException e) {
            log.error("Error while watching input folder", e);
        }
    }
    
    private boolean isSupportedFile(Path path) {
        String fileName = path.getFileName().toString().toLowerCase();
        return fileWatcherProperties.getSupportedExtensions().stream()
                .anyMatch(ext -> fileName.endsWith("." + ext));
    }
    
    private void processFileAsync(Path filePath) {
        String filePathStr = filePath.toString();
        processingFiles.add(filePathStr);
        
        // Check file size before processing
        boolean isLargeFile = isLargeFile(filePath);
        if (isLargeFile) {
            log.warn("Large file detected: {} ({}MB). Processing with special handling.", 
                filePath.getFileName(), getFileSizeMB(filePath));
        }
        
        CompletableFuture<Void> processingTask = CompletableFuture.runAsync(() -> {
            try {
                // Acquire semaphore to limit concurrent processing
                processingLimiter.acquire();
                log.debug("Started processing file: {} (Permits available: {})", 
                    filePath.getFileName(), processingLimiter.availablePermits());
                
                processJsonFile(filePath, isLargeFile);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Processing interrupted for file: {}", filePath.getFileName(), e);
                moveToErrorFolder(filePath, "Processing interrupted: " + e.getMessage());
            } catch (Exception e) {
                log.error("Unexpected error processing file: {}", filePath.getFileName(), e);
                moveToErrorFolder(filePath, "Unexpected error: " + e.getMessage());
            } finally {
                processingLimiter.release();
                processingFiles.remove(filePathStr);
                log.debug("Finished processing file: {} (Permits available: {})", 
                    filePath.getFileName(), processingLimiter.availablePermits());
            }
        });
        
        // Add timeout handling
        processingTask.orTimeout(fileWatcherProperties.getProcessingTimeoutMinutes(), TimeUnit.MINUTES)
            .whenComplete((result, throwable) -> {
                if (throwable instanceof TimeoutException) {
                    log.error("Processing timeout for file: {} after {} minutes", 
                        filePath.getFileName(), fileWatcherProperties.getProcessingTimeoutMinutes());
                    moveToErrorFolder(filePath, "Processing timeout after " + 
                        fileWatcherProperties.getProcessingTimeoutMinutes() + " minutes");
                    processingFiles.remove(filePathStr);
                } else if (throwable != null) {
                    log.error("Processing failed for file: {}", filePath.getFileName(), throwable);
                }
            });
    }
    
    private boolean isLargeFile(Path filePath) {
        try {
            long fileSizeBytes = Files.size(filePath);
            long fileSizeMB = fileSizeBytes / (1024 * 1024);
            return fileSizeMB > fileWatcherProperties.getLargeFileThresholdMb();
        } catch (IOException e) {
            log.warn("Could not determine file size for: {}", filePath, e);
            return false;
        }
    }
    
    private long getFileSizeMB(Path filePath) {
        try {
            long fileSizeBytes = Files.size(filePath);
            return fileSizeBytes / (1024 * 1024);
        } catch (IOException e) {
            log.warn("Could not determine file size for: {}", filePath, e);
            return 0;
        }
    }
    
    private void processJsonFile(Path filePath, boolean isLargeFile) {
        String fileName = filePath.getFileName().toString();
        log.info("Processing file: {} (Large file: {})", fileName, isLargeFile);
        
        // Disable auto-commit for large files if configured
        boolean originalAutoCommit = true;
        if (isLargeFile && fileWatcherProperties.isDisableAutoCommitForLargeFiles()) {
            log.info("Disabling auto-commit for large file: {}", fileName);
            // Note: In a real application, you would disable auto-commit on your database connection
            // This is a placeholder for the actual implementation
            originalAutoCommit = false;
        }
        
        try {
            // Read and parse JSON file
            ScrapeRequestBatch batch = parseJsonFile(filePath);
            if (batch == null || batch.getScrapeRequests() == null || batch.getScrapeRequests().isEmpty()) {
                log.warn("No valid scrape requests found in file: {}", fileName);
                moveToErrorFolder(filePath, "No valid scrape requests found");
                return;
            }
            
            // Set batch metadata
            batch.setBatchId(generateBatchId(fileName));
            batch.setDescription("Batch processed from file: " + fileName + (isLargeFile ? " (Large File)" : ""));
            
            log.info("Found {} scrape requests in file: {}", batch.getTotalRequests(), fileName);
            
            // Process each scrape request and create ScrapeJob objects
            List<String> createdJobIds = new ArrayList<>();
            List<String> processingErrors = new ArrayList<>();

            for (ScrapeRequest request : batch.getScrapeRequests()) {
                try {
                    // Create ScrapeJob from ScrapeRequest
                    ScrapeJob scrapeJob = createScrapeJobFromRequest(request, batch.getBatchId());
                    fileBasedJobService.saveScrapeJob(scrapeJob);
                    createdJobIds.add(scrapeJob.getId());
                    log.debug("Created scrape job for company: {}", request.getName());
                } catch (Exception e) {
                    String error = "Failed to save scrape job for company: " + request.getName() + " - " + e.getMessage();
                    log.error(error, e);
                    processingErrors.add(error);
                }
            }

            // Create batch summary with processing errors
            createBatchSummary(batch, createdJobIds, fileName, processingErrors);
            
            // Manual commit for large files
            if (isLargeFile && fileWatcherProperties.isDisableAutoCommitForLargeFiles()) {
                log.info("Manually committing transaction for large file: {}", fileName);
                // Note: In a real application, you would commit the transaction here
                // This is a placeholder for the actual implementation
            }
            
            // Move processed file
            moveToProcessedFolder(filePath);
            
            log.info("Successfully processed file: {} with {} requests ({} errors)", 
                fileName, batch.getTotalRequests(), processingErrors.size());
            
        } catch (Exception e) {
            log.error("Error processing file: {}", fileName, e);
            moveToErrorFolder(filePath, e.getMessage());
        } finally {
            // Re-enable auto-commit if it was disabled
            if (isLargeFile && fileWatcherProperties.isDisableAutoCommitForLargeFiles() && !originalAutoCommit) {
                log.info("Re-enabling auto-commit after processing large file: {}", fileName);
                // Note: In a real application, you would re-enable auto-commit here
            }
        }
    }
    
    private ScrapeRequestBatch parseJsonFile(Path filePath) throws IOException {
        // Try to parse as ScrapeRequestBatch first
        try {
            return objectMapper.readValue(filePath.toFile(), ScrapeRequestBatch.class);
        } catch (Exception e) {
            log.debug("Failed to parse as ScrapeRequestBatch, trying as List<ScrapeRequest>");
        }
        
        // Try to parse as direct list of ScrapeRequests
        try {
            List<ScrapeRequest> requests = objectMapper.readValue(
                filePath.toFile(), 
                new TypeReference<List<ScrapeRequest>>() {}
            );
            return new ScrapeRequestBatch(requests);
        } catch (Exception e) {
            log.error("Failed to parse JSON file as either ScrapeRequestBatch or List<ScrapeRequest>: {}", filePath, e);
            throw e;
        }
    }
    
    private ScrapeJob createScrapeJobFromRequest(ScrapeRequest request, String batchId) {
        ScrapeJob scrapeJob = new ScrapeJob();
        scrapeJob.setName(request.getName());
        scrapeJob.setGlassdoor(request.getGlassdoor());
        scrapeJob.setClutch(request.getClutch());
        scrapeJob.setGoodfirms(request.getGoodfirms());
        scrapeJob.setWebsite(request.getWebsite());
        scrapeJob.setLinkedin(request.getLinkedin());
        scrapeJob.setStatus(ScrapeStatus.QUEUE);
        scrapeJob.setRetryCount(0);
        scrapeJob.setMaxRetries(3);
        scrapeJob.setCsvFileName(request.getCsvFileName());
        scrapeJob.setCsvUploadId(request.getCsvUploadId());
        scrapeJob.setBatchId(batchId);
        scrapeJob.setCreatedAt(LocalDateTime.now());
        scrapeJob.setUpdatedAt(LocalDateTime.now());
        scrapeJob.setLastModifiedBy("FILE_WATCHER");
        return scrapeJob;
    }
    
    private String generateBatchId(String fileName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String cleanFileName = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
        return "batch_" + cleanFileName + "_" + timestamp;
    }
    
    // Note: In file-based approach, we don't wait for completion here
    // The scheduler will handle processing the jobs we created
    
    // Note: In file-based approach, completed scrape data is saved by the scheduler
    // when jobs are completed, not by the file watcher
    
    private void createBatchSummary(ScrapeRequestBatch batch, List<String> createdJobIds, String fileName, List<String> processingErrors) {
        try {
            Map<String, Object> summary = new HashMap<>();
            summary.put("batchId", batch.getBatchId());
            summary.put("originalFileName", fileName);
            summary.put("processedAt", LocalDateTime.now());
            summary.put("totalRequests", batch.getTotalRequests());
            summary.put("successfullyCreated", createdJobIds.size());
            summary.put("failedRequests", processingErrors.size());
            summary.put("createdJobIds", createdJobIds);
            summary.put("processingErrors", processingErrors);
            summary.put("description", batch.getDescription());

            String summaryFileName = "summary_" + batch.getBatchId() + ".json";
            Path summaryPath = Paths.get(fileWatcherProperties.getOutputFolder(), summaryFileName);

            objectMapper.writerWithDefaultPrettyPrinter().writeValue(summaryPath.toFile(), summary);
            log.info("Created batch summary: {} (Successful: {}, Failed: {})",
                summaryFileName, createdJobIds.size(), processingErrors.size());

        } catch (Exception e) {
            log.error("Failed to create batch summary for file: {}", fileName, e);
        }
    }
    
    private void moveToProcessedFolder(Path filePath) {
        moveFile(filePath, fileWatcherProperties.getProcessedFolder(), "processed");
    }
    
    private void moveToErrorFolder(Path filePath, String errorMessage) {
        try {
            // Create error info file
            String errorFileName = filePath.getFileName().toString() + ".error";
            Path errorInfoPath = Paths.get(fileWatcherProperties.getErrorFolder(), errorFileName);
            
            Map<String, Object> errorInfo = Map.of(
                "originalFile", filePath.getFileName().toString(),
                "errorMessage", errorMessage,
                "errorTime", LocalDateTime.now()
            );
            
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(errorInfoPath.toFile(), errorInfo);
        } catch (Exception e) {
            log.error("Failed to create error info file", e);
        }
        
        moveFile(filePath, fileWatcherProperties.getErrorFolder(), "error");
    }
    
    private void moveFile(Path sourcePath, String targetFolder, String operation) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String originalFileName = sourcePath.getFileName().toString();
            String newFileName = timestamp + "_" + originalFileName;
            
            Path targetPath = Paths.get(targetFolder, newFileName);
            Files.move(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("Moved file to {} folder: {} -> {}", operation, originalFileName, newFileName);
            
        } catch (IOException e) {
            log.error("Failed to move file to {} folder: {}", operation, sourcePath, e);
        }
    }
    
    // Note: In file-based approach, completed scrapes are automatically saved
    // by the scheduler when jobs complete, so no fallback export is needed
    
    // Note: Fallback export method removed - not needed in file-based approach
    
    private String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unknown";
        }
        return fileName.replaceAll("[^a-zA-Z0-9._-]", "_").substring(0, Math.min(fileName.length(), 50));
    }
} 