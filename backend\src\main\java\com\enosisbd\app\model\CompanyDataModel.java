package com.enosisbd.app.model;

import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyDataModel {
    
    // Basic company information
    private String clutchName;
    private String clutchSlug;
    private String description;
    private String clutchFoundedYear;
    private String headquarters;
    private String clutchCompanySize;
    private String industry;
    private String specialties;
    private String clutchProviderName;
    
    // Clutch-specific comprehensive data
    private String clutchCompanyLogoURL;
    private String clutchCompanyOverview;
    private String clutchCompanyUrl;
    private String clutchProfileUrl;
    private Integer clutchTotalReviews;
    private Integer clutchMinimumProjectSize;
    private String clutchHourlyRateRange;
    private String clutchCity;
    private String clutchCountry;
    
    // Rating information
    private Double clutchRating; // Overall rating
    private String clutchQuality; // Quality rating
    private String clutchSchedule; // Schedule rating
    private String clutchCost; // Cost rating
    private Boolean clutchWillingToRefer; // Willing to refer rating
    
    // Verification information
    private Boolean clutchVerificationBadge;
    private Boolean clutchVerified;
    private String clutchVerificationBadgeText;
    
    // Services and industries
    private List<String> clutchAllServices;
    private List<String> clutchAllIndustries;
    private List<String> clutchTopMentions;
    
    // Legacy Clutch-specific data (keeping for backward compatibility)
    private Integer clutchReviewsCount;
    private String clutchLocation;
    private String clutchEmployees;
    private String clutchHourlyRate;
    private String clutchMinProjectSize;
    
    // Goodfirms-specific data
    private Double goodfirmsRating;
    private Integer goodfirmsReviewsCount;
    private String goodfirmsLocation;
    private String goodfirmsEmployees;
    private String goodfirmsServices;
    
    // Additional Goodfirms-specific fields
    private String goodfirmProviderName;
    private String goodfirmRatings;
    private Boolean goodfirmVerificationBadge;
    private String goodfirmProfileUrl;
    private List<String> clientLikes;
    private List<String> clientDislikes;
    
    // Common fields used by multiple scrapers
    private String name;
    private String companyLogoURL;
    private String city;
    private String country;
    private String companyOverview;
    
    // Glassdoor-specific data
    private Double glassdoorRating;
    private Integer glassdoorReviewsCount;
    private Double glassdoorEmployeeSatisfaction;
    
    // Additional Glassdoor-specific fields
    private Map<String, Double> categoryRatings;
    private Map<String, String> ratingsDistribution;
    private List<String> pros;
    private List<String> cons;
    
    // LinkedIn-specific data
    private Integer linkedinFollowers;
    private Integer linkedinEmployeesCount;
    private String linkedinIndustry;
    
    // Website-specific data
    private String websiteTitle;
    private String websiteDescription;
    private String websiteKeywords;
    
    // Metadata
    private String source; // Which platform this data was scraped from
    private LocalDateTime scrapedAt;
    private Boolean isSuccessful;
    private String errorMessage;

    /**
     * Factory method to create a failed scraping result
     */
    public static CompanyDataModel failed(String source, String errorMessage) {
        return CompanyDataModel.builder()
                .source(source)
                .isSuccessful(false)
                .errorMessage(errorMessage)
                .scrapedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * Factory method to create a successful scraping result
     */
    public static CompanyDataModel success(String source) {
        return CompanyDataModel.builder()
                .source(source)
                .isSuccessful(true)
                .scrapedAt(LocalDateTime.now())
                .build();
    }
}
