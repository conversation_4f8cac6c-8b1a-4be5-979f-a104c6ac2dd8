package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Represents a job file (input file) containing scrape requests
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class JobFile {
    
    private String fileName;
    private String filePath;
    private LocalDateTime uploadedAt;
    private LocalDateTime startedAt;
    private LocalDateTime completedAt;
    private JobFileStatus status;
    private int totalCompanies;
    private int processedCompanies;
    private int successfulCompanies;
    private int failedCompanies;
    private List<String> companyIds; // List of company IDs in this job
    private String errorMessage;
    
    public enum JobFileStatus {
        UPLOADED,
        PROCESSING,
        COMPLETED,
        FAILED
    }
    
    /**
     * Calculate progress percentage
     */
    public double getProgressPercentage() {
        if (totalCompanies == 0) return 0.0;
        return (double) processedCompanies / totalCompanies * 100.0;
    }
    
    /**
     * Check if job is running
     */
    public boolean isRunning() {
        return status == JobFileStatus.PROCESSING;
    }
    
    /**
     * Check if job is completed
     */
    public boolean isCompleted() {
        return status == JobFileStatus.COMPLETED || status == JobFileStatus.FAILED;
    }
}
