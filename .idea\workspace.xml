<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="686e1f5a-4a9d-4f55-90e5-ea45d62bb165" name="Changes" comment="Integrated Frontend and Backend. &#10;&#10;Currently, Frontend will upload a CSV file and Backend will process and store the data in the file system.&#10;&#10;Project: EOS TOOLS. #Integration.">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/controller/ScraperController.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/controller/ScraperController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/controller/request/ScrapeRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/controller/request/ScrapeRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/entity/ScrapeEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/entity/ScrapeEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/repository/ScrapeRepository.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/repository/ScrapeRepository.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/ClutchScraperService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/ClutchScraperService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/GlassdoorScraperService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/GlassdoorScraperService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/GoodfirmsScraperService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/GoodfirmsScraperService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/ScraperService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/ScraperService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/WebsiteScraperService.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/WebsiteScraperService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/ScraperServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/ScraperServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/clutch/ClutchScraperServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/clutch/ClutchScraperServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/glassdoor/GlassdoorScraperServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/glassdoor/GlassdoorScraperServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/goodfirms/GoodfirmsScraperServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/goodfirms/GoodfirmsScraperServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/website/WebsiteCrawlerServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/main/java/com/enosisbd/app/service/impl/website/WebsiteCrawlerServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/eos-ui/src/App.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/eos-ui/src/App.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/eos-ui/src/components/dashboard/scraping-jobs.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/eos-ui/src/components/dashboard/scraping-jobs.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/eos-ui/src/components/layout/sidebar.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/eos-ui/src/components/layout/sidebar.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="2z4pIsgigg39IpSEnOsU8JZ2zeb" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.Application.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/EOS/eos"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="Application" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.enosisbd.app.Application" />
      <module name="app" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.enosisbd.app.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.Application" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="686e1f5a-4a9d-4f55-90e5-ea45d62bb165" name="Changes" comment="" />
      <created>1751000122785</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751000122785</updated>
    </task>
    <task id="LOCAL-00001" summary="Added Backend Part.">
      <option name="closed" value="true" />
      <created>1751000201306</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751000201306</updated>
    </task>
    <task id="LOCAL-00002" summary="Frontend Cleared.">
      <option name="closed" value="true" />
      <created>1751001919858</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751001919858</updated>
    </task>
    <task id="LOCAL-00003" summary="Integrated Frontend and Backend. &#10;&#10;Currently, Frontend will upload a CSV file and Backend will process and store the data in the file system.&#10;&#10;Project: EOS TOOLS. #Integration.">
      <option name="closed" value="true" />
      <created>1751029468246</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751029468246</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="application.yaml" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Added Backend Part." />
    <MESSAGE value="Frontend Cleared." />
    <MESSAGE value="Integrated Frontend and Backend. &#10;&#10;Currently, Frontend will upload a CSV file and Backend will process and store the data in the file system.&#10;&#10;Project: EOS TOOLS. #Integration." />
    <option name="LAST_COMMIT_MESSAGE" value="Integrated Frontend and Backend. &#10;&#10;Currently, Frontend will upload a CSV file and Backend will process and store the data in the file system.&#10;&#10;Project: EOS TOOLS. #Integration." />
  </component>
</project>