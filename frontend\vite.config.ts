import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [react(), tailwindcss()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 5173,
  },
  define: {
    // Make environment variables available at build time
    __API_BASE_URL__: JSON.stringify(
      process.env.VITE_API_BASE_URL || "http://localhost:8080"
    ),
  },
});
