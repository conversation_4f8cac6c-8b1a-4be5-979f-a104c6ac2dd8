package com.enosisbd.app.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.List;
import java.util.ArrayList;
import java.util.UUID;

// --- Company ---
@Data // Generates getters, setters, toString, equals, and hashCode
@NoArgsConstructor // Generates a no-argument constructor
@AllArgsConstructor // Generates an all-argument constructor
@Builder // Provides a builder pattern for object creation
public class WebsiteInfo {
    private UUID companyId;
    private String name;
    private List<String> aliases = new ArrayList<>(); // Initialize to avoid null pointer
    private String url;
    private String headquartersLocation;
    private Integer foundedYear;
    private String fundingStatus;
    private String employeeSize;
    private String cultureDescription;
    private String focusStatement;
    private String outcomeSummary;
    private OtherCompanyInfo otherInfo;

    // For Many-to-Many relationship with Industry (managed through CompanyIndustry table)
    // In a real JPA/Hibernate setup, this would often be mapped with @ManyToMany
    private List<Industry> industries = new ArrayList<>();

    // For One-to-Many relationship with ProductService
    // In a real JPA/Hibernate setup, this would often be mapped with @OneToMany
    private List<ProductService> productServices = new ArrayList<>();

    // For One-to-Many relationship with Achievement
    // In a real JPA/Hibernate setup, this would often be mapped with @OneToMany
    private List<Achievement> achievements = new ArrayList<>();

    // Nested object for Company's less structured info
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OtherCompanyInfo {
        private List<String> globalPresence = new ArrayList<>();
        private String foundingStory;
        private List<String> initiatives = new ArrayList<>();
    }
}

