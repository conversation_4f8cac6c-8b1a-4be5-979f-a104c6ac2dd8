package com.enosisbd.app.service.impl.glassdoor;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.enosisbd.app.model.CompanyDataModel;
import com.enosisbd.app.service.GlassdoorScraperService;
import com.enosisbd.app.service.crawler.HeaderGenerator;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitUntilState;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class GlassdoorScraperServiceImpl implements GlassdoorScraperService {
    
    @Value("${scraping.timeout-ms:120000}")
    private int TIMEOUT_MS;
    
    @Value("${scraping.glassdoor.login-required:false}")
    private boolean loginRequired;
    
    @Value("${scraping.anti-detection.enabled:true}")
    private boolean antiDetectionEnabled;
    
    @Value("${scraping.glassdoor.page-load-timeout-ms:30000}")
    private int pageLoadTimeoutMs;
    
    @Value("${scraping.glassdoor.ratings-timeout-ms:20000}")
    private int ratingsTimeoutMs;
    
    @Value("${scraping.retry.max-attempts:3}")
    private int maxRetryAttempts;
    
    @Value("${scraping.retry.backoff-multiplier:2000}")
    private long backoffMultiplier;
    
    private final HeaderGenerator headerGenerator;
    
    @Override
    public CompanyDataModel scrapeFromGlassdoor(String companyUrl) {
        log.info("Starting to scrape Glassdoor data for company: {}", companyUrl);
        CompanyDataModel companyDataModel = CompanyDataModel.success("glassdoor");
        
        int maxRetries = maxRetryAttempts;
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                log.info("Glassdoor scraping attempt {} of {} for URL: {}", retryCount + 1, maxRetries, companyUrl);
                
                // Use the actual URL parameter instead of hardcoded
                scrapeGlassdoorData(companyUrl, companyDataModel);
                
                log.info("✅ Successfully scraped Glassdoor data for {}", companyUrl);
                return companyDataModel;
                
            } catch (Exception e) {
                retryCount++;
                log.error("Error scraping Glassdoor data for company {} (attempt {} of {}): {}", 
                         companyUrl, retryCount, maxRetries, e.getMessage());
                
                if (retryCount < maxRetries) {
                    try {
                        // Wait before retrying (exponential backoff)
                        long waitTime = backoffMultiplier * retryCount;
                        log.info("Waiting {} ms before retry...", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Interrupted while waiting for retry");
                        break;
                    }
                } else {
                    log.error("All retry attempts failed for Glassdoor scraping: {}", companyUrl);
                    return CompanyDataModel.failed("glassdoor", 
                        String.format("Failed after %d attempts. Last error: %s", maxRetries, e.getMessage()));
                }
            }
        }
        
        return CompanyDataModel.failed("glassdoor", "Scraping failed after all retry attempts");
    }
    
    private void scrapeGlassdoorData(String url, CompanyDataModel companyDataModel) {
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                .setHeadless(!antiDetectionEnabled ? true : false) // Use headless based on anti-detection settings
            );
            
            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setUserAgent(headerGenerator.generateHeaders(false, null).get("User-Agent"))
                    .setExtraHTTPHeaders(headerGenerator.generateHeaders(false, "https://www.google.com/")));
            
            context.setDefaultTimeout(TIMEOUT_MS);
            Page page = context.newPage();

            log.info("Navigating to Glassdoor URL: {}", url);
            page.navigate(url, new Page.NavigateOptions()
                .setWaitUntil(WaitUntilState.DOMCONTENTLOADED)
                .setTimeout(pageLoadTimeoutMs)
            );

            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            log.info("Successfully loaded the Glassdoor page content.");

            // Extract company name from page if available
            try {
                String companyName = page.locator("h1, .employerName, [data-test='employer-name']").first().textContent();
                if (companyName != null && !companyName.trim().isEmpty()) {
                    companyDataModel.setName(companyName.trim());
                    log.info("Extracted company name: {}", companyName);
                }
            } catch (Exception e) {
                log.warn("Could not extract company name: {}", e.getMessage());
            }

            // Extract ratings data
            try {
                log.info("Extracting ratings data...");
                page.waitForSelector("[data-test='industry-average-and-distribution']", 
                    new Page.WaitForSelectorOptions().setTimeout(ratingsTimeoutMs));
                String ratingsHtml = page.locator("[data-test='industry-average-and-distribution']").innerHTML();
                parseRatingsHtml(ratingsHtml, companyDataModel);
                log.info("Successfully extracted ratings data");
            } catch (Exception e) {
                log.warn("Could not extract ratings data: {}", e.getMessage());
            }

            // Extract pros and cons
            try {
                log.info("Extracting pros and cons data...");
                page.waitForSelector(".ReviewHighlights_hightlightContainer__tw9Kl", 
                    new Page.WaitForSelectorOptions().setTimeout(ratingsTimeoutMs));
                String prosConsHtml = page.locator(".ReviewHighlights_hightlightContainer__tw9Kl").innerHTML();
                parseProsConsHtml(prosConsHtml, companyDataModel);
                log.info("Successfully extracted pros and cons data");
            } catch (Exception e) {
                log.warn("Could not extract pros and cons data: {}", e.getMessage());
            }

            // Extract overall rating if available
            try {
                String overallRatingText = page.locator("[data-test='rating'], .rating, .ratingNumber").first().textContent();
                if (overallRatingText != null && !overallRatingText.trim().isEmpty()) {
                    Double rating = Double.parseDouble(overallRatingText.trim());
                    companyDataModel.setGlassdoorRating(rating);
                    log.info("Extracted overall rating: {}", rating);
                }
            } catch (Exception e) {
                log.warn("Could not extract overall rating: {}", e.getMessage());
            }

            // Extract reviews count if available
            try {
                String reviewsText = page.locator("[data-test='reviews-count'], .reviewsCount").first().textContent();
                if (reviewsText != null && !reviewsText.trim().isEmpty()) {
                    String numericPart = reviewsText.replaceAll("[^0-9]", "");
                    if (!numericPart.isEmpty()) {
                        Integer reviewsCount = Integer.parseInt(numericPart);
                        companyDataModel.setGlassdoorReviewsCount(reviewsCount);
                        log.info("Extracted reviews count: {}", reviewsCount);
                    }
                }
            } catch (Exception e) {
                log.warn("Could not extract reviews count: {}", e.getMessage());
            }

            browser.close();
            log.info("Successfully scraped Glassdoor data for {}.", url);
        }
    }

    private CompanyDataModel parseRatingsHtml(String htmlContent, CompanyDataModel companyDataModel) {
        Document doc = Jsoup.parse(htmlContent);

        // --- Ratings by Category ---
        Map<String, Double> categoryRatings = new LinkedHashMap<>();
        // STRATEGY: Find all links ('a' tags) where the href contains "filter.searchCategory=".
        // This is a very stable and reliable way to find the rating items.
        Elements ratingItems = doc.select("a[href*='filter.searchCategory=']");
        log.info("Found {} category rating items using stable href selector.", ratingItems.size());

        for (Element item : ratingItems) {
            // Find all <p> tags within the link.
            Elements pTags = item.select("p");
            String label = null;
            Double rating = null;

            for (Element pTag : pTags) {
                String text = pTag.text().trim();
                try {
                    // If the text can be parsed to a number, it's the rating.
                    rating = Double.parseDouble(text);
                } catch (NumberFormatException e) {
                    // Otherwise, it's the label.
                    label = text;
                }
            }

            if (label != null && rating != null) {
                categoryRatings.put(label, rating);
            } else {
                log.warn("Could not parse a category rating from item: {}", item.html());
            }
        }
        companyDataModel.setCategoryRatings(categoryRatings);

        // --- Ratings Distribution ---
        Map<String, String> ratingsDistribution = new LinkedHashMap<>();
        // STRATEGY: Find all links where the href contains "filter.overallRating=".
        Elements distributionItems = doc.select("a[href*='filter.overallRating=']");
        log.info("Found {} rating distribution items using stable href selector.", distributionItems.size());

        for (Element item : distributionItems) {
            String starLabel = item.select("p:contains(stars)").text();
            String percentLabel = item.select("p:contains(%)").text();

            if (!starLabel.isEmpty() && !percentLabel.isEmpty()) {
                ratingsDistribution.put(starLabel.trim(), percentLabel.trim());
            } else {
                log.warn("Could not parse a rating distribution from item: {}", item.html());
            }
        }
        companyDataModel.setRatingsDistribution(ratingsDistribution);

        return companyDataModel;
    }

    /**
     * This method is already using the most stable selector available: the 'data-test' attribute.
     * No changes are needed here as this is considered a best practice.
     */
    private CompanyDataModel parseProsConsHtml(String htmlContent, CompanyDataModel companyDataModel) {
        Document doc = Jsoup.parse(htmlContent);
        List<String> pros = new ArrayList<>();
        List<String> cons = new ArrayList<>();

        Element prosContainer = doc.selectFirst("div:contains(Pros)");
        if (prosContainer != null) {
            Elements prosItems = prosContainer.parent().select("li[data-test='review-highlight-text']");
            for (Element item : prosItems) {
                pros.add(item.text().trim());
            }
        }

        Element consContainer = doc.selectFirst("div:contains(Cons)");
        if (consContainer != null) {
            Elements consItems = consContainer.parent().select("li[data-test='review-highlight-text']");
            for (Element item : consItems) {
                cons.add(item.text().trim());
            }
        }

        companyDataModel.setPros(pros);
        companyDataModel.setCons(cons);
        return companyDataModel;
    }
}
