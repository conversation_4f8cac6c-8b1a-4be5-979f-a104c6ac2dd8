{"groups": [{"name": "file-based-job", "type": "com.enosisbd.app.config.FileBasedJobProperties", "sourceType": "com.enosisbd.app.config.FileBasedJobProperties"}, {"name": "file-watcher", "type": "com.enosisbd.app.config.FileWatcherProperties", "sourceType": "com.enosisbd.app.config.FileWatcherProperties"}, {"name": "scraping.scheduler", "type": "com.enosisbd.app.config.SchedulingConfig", "sourceType": "com.enosisbd.app.config.SchedulingConfig"}], "properties": [{"name": "file-based-job.auto-create-folders", "type": "java.lang.Bo<PERSON>an", "description": "Whether to automatically create folders if they don't exist", "sourceType": "com.enosisbd.app.config.FileBasedJobProperties", "defaultValue": true}, {"name": "file-based-job.companies-folder", "type": "java.lang.String", "description": "Directory where company data files are stored", "sourceType": "com.enosisbd.app.config.FileBasedJobProperties", "defaultValue": "./companies"}, {"name": "file-based-job.jobs-folder", "type": "java.lang.String", "description": "Directory where job files are stored", "sourceType": "com.enosisbd.app.config.FileBasedJobProperties", "defaultValue": "./jobs"}, {"name": "file-watcher.auto-create-folders", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": true}, {"name": "file-watcher.disable-auto-commit-for-large-files", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": true}, {"name": "file-watcher.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": true}, {"name": "file-watcher.error-folder", "type": "java.lang.String", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": "./error"}, {"name": "file-watcher.input-folder", "type": "java.lang.String", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": "./input"}, {"name": "file-watcher.large-file-threshold-mb", "type": "java.lang.Integer", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": 5}, {"name": "file-watcher.max-concurrent-files", "type": "java.lang.Integer", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": 3}, {"name": "file-watcher.output-folder", "type": "java.lang.String", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": "./output"}, {"name": "file-watcher.polling-interval-seconds", "type": "java.lang.Integer", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": 5}, {"name": "file-watcher.processed-folder", "type": "java.lang.String", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": "./processed"}, {"name": "file-watcher.processing-timeout-minutes", "type": "java.lang.Integer", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": 1}, {"name": "file-watcher.supported-extensions", "type": "java.util.List<java.lang.String>", "sourceType": "com.enosisbd.app.config.FileWatcherProperties", "defaultValue": "json"}, {"name": "scraping.scheduler.default-max-retries", "type": "java.lang.Integer", "description": "Default maximum retry attempts for failed scraping operations", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 3}, {"name": "scraping.scheduler.maintenance-job-initial-delay-minutes", "type": "java.lang.Integer", "description": "Initial delay before starting maintenance job in minutes", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 10}, {"name": "scraping.scheduler.maintenance-job-interval-minutes", "type": "java.lang.Integer", "description": "Interval for running maintenance job in minutes", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 30}, {"name": "scraping.scheduler.max-additional-random-minutes", "type": "java.lang.Integer", "description": "Maximum additional random minutes to add to the minimum gap", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 30}, {"name": "scraping.scheduler.max-concurrent-processing", "type": "java.lang.Integer", "description": "Maximum number of concurrent scraping operations", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 1}, {"name": "scraping.scheduler.min-gap-minutes", "type": "java.lang.Integer", "description": "Minimum gap between scraping operations in minutes", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 30}, {"name": "scraping.scheduler.processing-job-initial-delay-minutes", "type": "java.lang.Integer", "description": "Initial delay before starting processing job in minutes", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 1}, {"name": "scraping.scheduler.processing-job-interval-minutes", "type": "java.lang.Integer", "description": "Interval for running the processing job in minutes", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 2}, {"name": "scraping.scheduler.scheduling-job-initial-delay-minutes", "type": "java.lang.Integer", "description": "Initial delay before starting scheduling job in minutes", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 2}, {"name": "scraping.scheduler.scheduling-job-interval-minutes", "type": "java.lang.Integer", "description": "Interval for running the scheduling job in minutes", "sourceType": "com.enosisbd.app.config.SchedulingConfig", "defaultValue": 5}], "hints": [], "ignored": {"properties": []}}