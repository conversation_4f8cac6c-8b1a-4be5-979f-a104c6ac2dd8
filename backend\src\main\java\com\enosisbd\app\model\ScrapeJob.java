package com.enosisbd.app.model;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Scrape job data model for file-based storage
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ScrapeJob {
    
    private String id; // Unique identifier for the scrape job
    private String name;
    private String glassdoor;
    private String clutch;
    private String goodfirms;
    private String website;
    private String linkedin;
    private ScrapeStatus status;
    private LocalDateTime nextScheduledTime;
    private LocalDateTime lastProcessedTime;
    private Integer retryCount = 0;
    private Integer maxRetries = 3;
    
    private String csvFileName;
    private String csvUploadId;
    private String batchId; // Batch ID for file processing

    private String companyId; // Reference to the company
    
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String lastModifiedBy;
    
    // Additional fields for tracking
    private String errorMessage;
    private String processInstanceId; // Flowable process instance ID if using workflow
}
