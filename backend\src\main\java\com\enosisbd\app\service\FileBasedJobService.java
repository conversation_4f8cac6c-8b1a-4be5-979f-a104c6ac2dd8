package com.enosisbd.app.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.enosisbd.app.model.Company;
import com.enosisbd.app.model.JobFile;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.model.ScrapeStatus;

/**
 * Service interface for file-based job management
 */
public interface FileBasedJobService {
    
    /**
     * Get all running job files (files in input directory)
     */
    List<JobFile> getRunningJobs();
    
    /**
     * Get all completed job files (files in processed directory)
     */
    List<JobFile> getCompletedJobs();
    
    /**
     * Get job results for a specific completed job file
     */
    List<Company> getJobResults(String fileName);
    
    /**
     * Delete a completed job and all its associated output files
     */
    boolean deleteCompletedJob(String fileName);
    
    /**
     * Save a scrape job to file
     */
    void saveScrapeJob(ScrapeJob scrapeJob);
    
    /**
     * Get all scrape jobs with specific status
     */
    List<ScrapeJob> getScrapeJobsByStatus(ScrapeStatus status);
    
    /**
     * Update scrape job status
     */
    void updateScrapeJobStatus(String jobId, ScrapeStatus status);
    
    /**
     * Get scrape job by ID
     */
    Optional<ScrapeJob> getScrapeJobById(String jobId);
    
    /**
     * Save company data to output file
     */
    void saveCompanyData(Company company, String jobFileName);
    
    /**
     * Get company by ID
     */
    Optional<Company> getCompanyById(String companyId);
    
    /**
     * Save company data
     */
    void saveCompany(Company company);
    
    /**
     * Get all companies for a specific job file
     */
    List<Company> getCompaniesByJobFile(String jobFileName);
    
    /**
     * Move file from input to processed directory
     */
    void moveFileToProcessed(String fileName);
    
    /**
     * Get job file information
     */
    Optional<JobFile> getJobFileInfo(String fileName);
    
    /**
     * Update job file information
     */
    void updateJobFileInfo(JobFile jobFile);
    
    /**
     * Get items ready for scheduling
     */
    List<ScrapeJob> getItemsReadyForScheduling();
    
    /**
     * Get items ready for processing
     */
    List<ScrapeJob> getItemsReadyForProcessing();
    
    /**
     * Get most recently processed item
     */
    Optional<ScrapeJob> getMostRecentlyProcessedItem();
    
    /**
     * Get scrape jobs by CSV upload ID
     */
    List<ScrapeJob> getScrapeJobsByCsvUploadId(String csvUploadId);

    /**
     * Get all companies
     */
    List<Company> getAllCompanies();

    /**
     * Get CSV upload statistics
     */
    List<Map<String, Object>> getCsvUploadStatistics();
}
