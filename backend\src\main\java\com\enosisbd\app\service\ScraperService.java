package com.enosisbd.app.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.model.Company;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.model.ScrapeStatus;

public interface ScraperService {
    void saveScrapeRequest(ScrapeRequest scrapeRequest);

    /**
     * Get all scrape jobs with QUEUE status
     * @return List of ScrapeJob with QUEUE status
     */
    List<ScrapeJob> getAllQueuedScrapes();

    /**
     * Get all scrape jobs by status
     * @param status The status to filter by
     * @return List of ScrapeJob with given status
     */
    List<ScrapeJob> getAllScrapesByStatus(ScrapeStatus status);

    /**
     * Get all companies
     * @return List of all companies
     */
    List<Company> getAllCompanies();

    /**
     * Get company by ID
     * @param id Company ID
     * @return Optional Company
     */
    Optional<Company> getCompanyById(String id);

    /**
     * Get companies by data completeness score
     * @param minScore Minimum completeness score
     * @return List of companies with score >= minScore
     */
    List<Company> getCompaniesByCompleteness(Double minScore);

    List<Map<String, Object>> getCsvUploads();

    List<ScrapeJob> getScrapesByCsvUploadId(String csvUploadId);
}
