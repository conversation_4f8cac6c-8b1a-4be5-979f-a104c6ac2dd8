package com.enosisbd.app.service;

import com.enosisbd.app.model.ScrapeJob;

/**
 * Service interface for managing scrape scheduling operations
 */
public interface ScrapeSchedulerService {

    /**
     * Process items that are ready for scheduling
     * This method schedules QUEUE items with proper timing gaps
     */
    void scheduleReadyItems();

    /**
     * Process items that are scheduled and ready to be scraped
     * This method starts the actual scraping process for scheduled items
     */
    void processScheduledItems();

    /**
     * Calculate the next scheduled time for an item
     * @param job The scrape job to schedule
     * @return The calculated next scheduled time
     */
    void calculateAndSetNextScheduledTime(ScrapeJob job);

    /**
     * Start processing a scrape job
     * @param job The job to process
     */
    void startProcessing(ScrapeJob job);

    /**
     * Mark a scrape job as completed successfully
     * @param job The job that completed successfully
     */
    void markAsCompleted(ScrapeJob job);

    /**
     * Mark a scrape job as failed and schedule retry if applicable
     * @param job The job that failed
     * @param error The error message
     */
    void markAsFailed(ScrapeJob job, String error);
}