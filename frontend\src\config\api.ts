/**
 * API Configuration for EOS Tools Frontend
 */

// Get API base URL from environment variables
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080';

// API endpoints
export const API_ENDPOINTS = {
  // Scraping endpoints
  SCRAPING: {
    UPLOAD: '/api/scrapping/upload',
    SCRAPE: '/api/scrapping',
    CSV_UPLOADS: '/api/scrapping/csv-uploads',
    DOWNLOAD: '/api/scrapping/download',
    STATUS: '/api/scrapping/debug/status',
    QUEUED: '/api/scrapping/queued',
  },
  // Job management endpoints
  JOBS: {
    RUNNING: '/api/jobs/running',
    COMPLETED: '/api/jobs/completed',
    DELETE: '/api/jobs/completed',
  },
} as const;

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string): string => {
  return `${API_BASE_URL}${endpoint}`;
};

// Export configuration object
export const apiConfig = {
  baseUrl: API_BASE_URL,
  endpoints: API_ENDPOINTS,
  buildUrl: buildApiUrl,
} as const;

export default apiConfig;
