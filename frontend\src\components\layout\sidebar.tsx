import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight, Home, Webhook } from "lucide-react";
import React from "react";
import { <PERSON> } from "react-router-dom";

interface SidebarItem {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
}

const sidebarItems: SidebarItem[] = [
  { title: "Dashboard", icon: Home, href: "/" },
  { title: "Scraping Jobs", icon: Webhook, href: "/scraping" },
];

interface SidebarProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
}

export const Sidebar = React.memo(function Sidebar({
  isCollapsed,
  toggleSidebar,
}: SidebarProps) {
  return (
    <div
      className={cn(
        "relative flex flex-col border-r bg-background transition-all",
        isCollapsed ? "w-16" : "w-64",
        "h-[calc(100vh-56px)]" // Adjust for header
      )}
    >
      <ScrollArea className="flex-1 px-3 py-2">
        <div className="space-y-1">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            return (
              <Button
                key={item.href}
                variant="ghost"
                className={cn(
                  "w-full justify-start",
                  isCollapsed && "justify-center"
                )}
                asChild
              >
                <Link to={item.href}>
                  <Icon className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
                  {!isCollapsed && item.title}
                </Link>
              </Button>
            );
          })}
        </div>
      </ScrollArea>

      <div className="flex justify-center border-t p-2">
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className="h-8 w-8 rounded-full"
        >
          {isCollapsed ? (
            <ChevronRight className="h-5 w-5" />
          ) : (
            <ChevronLeft className="h-5 w-5" />
          )}
        </Button>
      </div>
    </div>
  );
});
