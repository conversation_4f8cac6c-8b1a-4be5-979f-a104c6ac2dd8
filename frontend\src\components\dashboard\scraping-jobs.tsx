import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { apiConfig } from "@/config/api";
import {
  CheckCircle2,
  ChevronDown,
  ChevronRight,
  Clock,
  FileDown,
  FileText,
  Loader2,
  RefreshCw,
  Trash2,
  Upload,
  XCircle,
} from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

// Types and interfaces for file-based workflow
interface JobFile {
  fileName: string;
  filePath: string;
  fileSize: number;
  createdAt: string;
  lastModified: string;
  status?: string;
  totalRequests?: number;
  completedRequests?: number;
  failedRequests?: number;
  completeness?: number;
}

interface UploadResponse {
  successCount: number;
  csvUploadId: string;
  csvFileName: string;
  errors?: string[];
}

interface Company {
  id?: string;
  name: string;
  website?: string;
  description?: string;
  industry?: string;
  size?: string;
  location?: string;
  founded?: string;
  revenue?: string;
  employees?: string;
  rating?: number;
  reviews?: number;
  technologies?: string[];
  services?: string[];
  awards?: string[];
  socialMedia?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
  contact?: {
    email?: string;
    phone?: string;
    address?: string;
  };
  scraperSources?: {
    clutch?: boolean;
    goodfirms?: boolean;
    glassdoor?: boolean;
    linkedin?: boolean;
    website?: boolean;
  };
  scrapedAt?: string;
  lastUpdated?: string;
}

// File-based job status configuration
const fileStatusConfig = {
  RUNNING: {
    icon: <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />,
    label: "Running",
    color: "bg-blue-100 text-blue-800",
  },
  COMPLETED: {
    icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
    label: "Completed",
    color: "bg-green-100 text-green-800",
  },
  ERROR: {
    icon: <XCircle className="h-4 w-4 text-red-500" />,
    label: "Error",
    color: "bg-red-100 text-red-800",
  },
};

const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  try {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(dateString));
  } catch {
    return "Invalid Date";
  }
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// API functions for file-based operations
const fetchRunningJobs = async (): Promise<JobFile[]> => {
  const response = await fetch(
    apiConfig.buildUrl(apiConfig.endpoints.JOBS.RUNNING)
  );
  if (!response.ok) throw new Error("Failed to fetch running jobs");
  return response.json();
};

const fetchCompletedJobs = async (): Promise<JobFile[]> => {
  const response = await fetch(
    apiConfig.buildUrl(apiConfig.endpoints.JOBS.COMPLETED)
  );
  if (!response.ok) throw new Error("Failed to fetch completed jobs");
  return response.json();
};

const fetchJobResults = async (filename: string): Promise<Company[]> => {
  const response = await fetch(
    apiConfig.buildUrl(
      `${apiConfig.endpoints.JOBS.COMPLETED}/${encodeURIComponent(filename)}`
    )
  );
  if (!response.ok) throw new Error("Failed to fetch job results");
  return response.json();
};

const deleteCompletedJob = async (filename: string): Promise<void> => {
  const response = await fetch(
    apiConfig.buildUrl(
      `${apiConfig.endpoints.JOBS.DELETE}/${encodeURIComponent(filename)}`
    ),
    {
      method: "DELETE",
    }
  );
  if (!response.ok) throw new Error("Failed to delete job");
};

const uploadCsvFile = async (file: File): Promise<UploadResponse> => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await fetch(
    apiConfig.buildUrl(apiConfig.endpoints.SCRAPING.UPLOAD),
    {
      method: "POST",
      body: formData,
    }
  );

  if (!response.ok) throw new Error("Failed to upload CSV file");
  return response.json();
};

export default function ScrapingJobs() {
  const [runningJobs, setRunningJobs] = useState<JobFile[]>([]);
  const [completedJobs, setCompletedJobs] = useState<JobFile[]>([]);
  const [selectedJobResults, setSelectedJobResults] = useState<
    Company[] | null
  >(null);
  const [selectedJobName, setSelectedJobName] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [expandedRunning, setExpandedRunning] = useState<string[]>([]);
  const [expandedCompleted, setExpandedCompleted] = useState<string[]>([]);

  // Polling interval for running jobs (5 seconds)
  const POLLING_INTERVAL = 5000;

  const loadRunningJobs = useCallback(async () => {
    try {
      const jobs = await fetchRunningJobs();
      setRunningJobs(jobs);
    } catch (error) {
      console.error("Error loading running jobs:", error);
      toast.error("Failed to load running jobs");
    }
  }, []);

  const loadCompletedJobs = useCallback(async () => {
    try {
      const jobs = await fetchCompletedJobs();
      setCompletedJobs(jobs);
    } catch (error) {
      console.error("Error loading completed jobs:", error);
      toast.error("Failed to load completed jobs");
    }
  }, []);

  const loadJobResults = async (filename: string) => {
    setIsLoading(true);
    try {
      const results = await fetchJobResults(filename);
      setSelectedJobResults(results);
      setSelectedJobName(filename);
    } catch (error) {
      console.error("Error loading job results:", error);
      toast.error("Failed to load job results");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteJob = async (filename: string) => {
    if (
      !confirm(
        `Are you sure you want to delete the job "${filename}"? This action cannot be undone.`
      )
    ) {
      return;
    }

    try {
      await deleteCompletedJob(filename);
      toast.success("Job deleted successfully");
      loadCompletedJobs(); // Refresh the list

      // Clear selected results if the deleted job was selected
      if (selectedJobName === filename) {
        setSelectedJobResults(null);
        setSelectedJobName("");
      }
    } catch (error) {
      console.error("Error deleting job:", error);
      toast.error("Failed to delete job");
    }
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith(".csv")) {
      toast.error("Please upload a CSV file");
      return;
    }

    setIsUploading(true);
    try {
      await uploadCsvFile(file);
      toast.success("CSV file uploaded successfully");
      loadRunningJobs(); // Refresh running jobs
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("Failed to upload CSV file");
    } finally {
      setIsUploading(false);
      // Reset the input
      event.target.value = "";
    }
  };

  const toggleExpanded = (type: "running" | "completed", filename: string) => {
    if (type === "running") {
      setExpandedRunning((prev) =>
        prev.includes(filename)
          ? prev.filter((f) => f !== filename)
          : [...prev, filename]
      );
    } else {
      setExpandedCompleted((prev) =>
        prev.includes(filename)
          ? prev.filter((f) => f !== filename)
          : [...prev, filename]
      );
    }
  };

  // Initial load
  useEffect(() => {
    loadRunningJobs();
    loadCompletedJobs();
  }, [loadRunningJobs, loadCompletedJobs]);

  // Polling for running jobs
  useEffect(() => {
    const interval = setInterval(() => {
      loadRunningJobs();
    }, POLLING_INTERVAL);

    return () => clearInterval(interval);
  }, [loadRunningJobs]);

  return (
    <div className="space-y-6">
      {/* File Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload CSV File
          </CardTitle>
          <CardDescription>
            Upload a CSV file containing company data to start scraping jobs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Input
              type="file"
              accept=".csv"
              onChange={handleFileUpload}
              disabled={isUploading}
              className="flex-1"
            />
            {isUploading && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Uploading...
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Running Scraping Jobs Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
            Running Scraping Jobs
            <Badge variant="secondary">{runningJobs.length}</Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={loadRunningJobs}
              className="ml-auto"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </CardTitle>
          <CardDescription>
            Jobs currently being processed (auto-refreshes every 5 seconds)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {runningJobs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No running jobs at the moment</p>
              <p className="text-sm">Upload a CSV file to start scraping</p>
            </div>
          ) : (
            <div className="space-y-2">
              {runningJobs.map((job) => (
                <div
                  key={job.fileName}
                  className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded("running", job.fileName)}
                        className="p-0 h-auto"
                      >
                        {expandedRunning.includes(job.fileName) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                      <FileText className="h-4 w-4 text-blue-500" />
                      <div>
                        <p className="font-medium">{job.fileName}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(job.fileSize)} • Started{" "}
                          {formatDate(job.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {fileStatusConfig.RUNNING.icon}
                      <Badge className={fileStatusConfig.RUNNING.color}>
                        {fileStatusConfig.RUNNING.label}
                      </Badge>
                    </div>
                  </div>

                  {expandedRunning.includes(job.fileName) && (
                    <div className="mt-4 pt-4 border-t space-y-2">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">File Path:</span>
                          <p className="text-muted-foreground">
                            {job.filePath}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">Last Modified:</span>
                          <p className="text-muted-foreground">
                            {formatDate(job.lastModified)}
                          </p>
                        </div>
                      </div>
                      {job.totalRequests && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>
                              {job.completedRequests || 0} / {job.totalRequests}
                            </span>
                          </div>
                          <Progress
                            value={job.completeness || 0}
                            className="h-2"
                          />
                          <p className="text-xs text-muted-foreground">
                            {job.completeness?.toFixed(1) || 0}% complete
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Completed Scraping Jobs Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5 text-green-500" />
            Completed Scraping Jobs
            <Badge variant="secondary">{completedJobs.length}</Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={loadCompletedJobs}
              className="ml-auto"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </CardTitle>
          <CardDescription>
            Completed jobs with scraped data available for viewing
          </CardDescription>
        </CardHeader>
        <CardContent>
          {completedJobs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No completed jobs yet</p>
              <p className="text-sm">Completed jobs will appear here</p>
            </div>
          ) : (
            <div className="space-y-2">
              {completedJobs.map((job) => (
                <div
                  key={job.fileName}
                  className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          toggleExpanded("completed", job.fileName)
                        }
                        className="p-0 h-auto"
                      >
                        {expandedCompleted.includes(job.fileName) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                      <FileText className="h-4 w-4 text-green-500" />
                      <div>
                        <p className="font-medium">{job.fileName}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(job.fileSize)} • Completed{" "}
                          {formatDate(job.lastModified)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => loadJobResults(job.fileName)}
                        disabled={isLoading}
                      >
                        <FileDown className="h-4 w-4 mr-2" />
                        View Results
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteJob(job.fileName)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      {fileStatusConfig.COMPLETED.icon}
                      <Badge className={fileStatusConfig.COMPLETED.color}>
                        {fileStatusConfig.COMPLETED.label}
                      </Badge>
                    </div>
                  </div>

                  {expandedCompleted.includes(job.fileName) && (
                    <div className="mt-4 pt-4 border-t space-y-2">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium">File Path:</span>
                          <p className="text-muted-foreground">
                            {job.filePath}
                          </p>
                        </div>
                        <div>
                          <span className="font-medium">Completed:</span>
                          <p className="text-muted-foreground">
                            {formatDate(job.lastModified)}
                          </p>
                        </div>
                      </div>
                      {job.totalRequests && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Results</span>
                            <span>
                              {job.completedRequests || 0} / {job.totalRequests}
                            </span>
                          </div>
                          <Progress
                            value={job.completeness || 0}
                            className="h-2"
                          />
                          <p className="text-xs text-muted-foreground">
                            {job.completeness?.toFixed(1) || 0}% success rate
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Job Results Display */}
      {selectedJobResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileDown className="h-5 w-5 text-blue-500" />
              Job Results: {selectedJobName}
              <Badge variant="secondary">
                {selectedJobResults.length} companies
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSelectedJobResults(null);
                  setSelectedJobName("");
                }}
                className="ml-auto"
              >
                <XCircle className="h-4 w-4" />
              </Button>
            </CardTitle>
            <CardDescription>
              Scraped company data from the selected job
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading results...</span>
              </div>
            ) : selectedJobResults.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <XCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No results found for this job</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Company Name</TableHead>
                        <TableHead>Website</TableHead>
                        <TableHead>Industry</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Size</TableHead>
                        <TableHead>Sources</TableHead>
                        <TableHead>Scraped At</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedJobResults.map((company, index) => (
                        <TableRow key={company.id || index}>
                          <TableCell className="font-medium">
                            {company.name}
                          </TableCell>
                          <TableCell>
                            {company.website ? (
                              <a
                                href={company.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                {company.website}
                              </a>
                            ) : (
                              "N/A"
                            )}
                          </TableCell>
                          <TableCell>{company.industry || "N/A"}</TableCell>
                          <TableCell>{company.location || "N/A"}</TableCell>
                          <TableCell>{company.size || "N/A"}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {company.scraperSources?.clutch && (
                                <Badge variant="outline" className="text-xs">
                                  Clutch
                                </Badge>
                              )}
                              {company.scraperSources?.goodfirms && (
                                <Badge variant="outline" className="text-xs">
                                  GoodFirms
                                </Badge>
                              )}
                              {company.scraperSources?.glassdoor && (
                                <Badge variant="outline" className="text-xs">
                                  Glassdoor
                                </Badge>
                              )}
                              {company.scraperSources?.linkedin && (
                                <Badge variant="outline" className="text-xs">
                                  LinkedIn
                                </Badge>
                              )}
                              {company.scraperSources?.website && (
                                <Badge variant="outline" className="text-xs">
                                  Website
                                </Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {company.scrapedAt
                              ? formatDate(company.scrapedAt)
                              : "N/A"}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {/* Export Options */}
                <div className="flex justify-end gap-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => {
                      const csvContent = convertToCSV(selectedJobResults);
                      downloadCSV(csvContent, `${selectedJobName}_results.csv`);
                    }}
                  >
                    <FileDown className="h-4 w-4 mr-2" />
                    Export as CSV
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Helper functions for CSV export
const convertToCSV = (companies: Company[]): string => {
  const headers = [
    "Name",
    "Website",
    "Description",
    "Industry",
    "Size",
    "Location",
    "Founded",
    "Revenue",
    "Employees",
    "Rating",
    "Reviews",
    "Technologies",
    "Services",
    "Awards",
    "LinkedIn",
    "Twitter",
    "Facebook",
    "Email",
    "Phone",
    "Address",
    "Clutch",
    "GoodFirms",
    "Glassdoor",
    "LinkedIn Source",
    "Website Source",
    "Scraped At",
    "Last Updated",
  ];

  const rows = companies.map((company) => [
    company.name || "",
    company.website || "",
    company.description || "",
    company.industry || "",
    company.size || "",
    company.location || "",
    company.founded || "",
    company.revenue || "",
    company.employees || "",
    company.rating?.toString() || "",
    company.reviews?.toString() || "",
    company.technologies?.join("; ") || "",
    company.services?.join("; ") || "",
    company.awards?.join("; ") || "",
    company.socialMedia?.linkedin || "",
    company.socialMedia?.twitter || "",
    company.socialMedia?.facebook || "",
    company.contact?.email || "",
    company.contact?.phone || "",
    company.contact?.address || "",
    company.scraperSources?.clutch ? "Yes" : "No",
    company.scraperSources?.goodfirms ? "Yes" : "No",
    company.scraperSources?.glassdoor ? "Yes" : "No",
    company.scraperSources?.linkedin ? "Yes" : "No",
    company.scraperSources?.website ? "Yes" : "No",
    company.scrapedAt || "",
    company.lastUpdated || "",
  ]);

  const csvContent = [headers, ...rows]
    .map((row) =>
      row.map((field) => `"${field.replace(/"/g, '""')}"`).join(",")
    )
    .join("\n");

  return csvContent;
};

const downloadCSV = (csvContent: string, filename: string) => {
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute("download", filename);
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
