package com.enosisbd.app.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Company data model for file-based storage
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Company {
    
    private String id; // Unique identifier for the company
    private String name;
    
    // Clutch-related fields
    private String clutchSlug;
    private String clutchProviderName;
    private String clutchCompanyLogoURL;
    private String clutchCompanyOverview;
    private String clutchCompanyUrl;
    private String clutchProfileUrl;
    private Double clutchRating;
    private Integer clutchReviewsCount;
    private String clutchLocation;
    private String clutchEmployees;
    private String clutchHourlyRate;
    private String clutchMinProjectSize;
    private String clutchFocusAreas;
    private String clutchClientFocusAreas;
    private String clutchPortfolioItems;
    private String clutchAwards;
    private String clutchCertifications;
    private String clutchKeyClients;
    private String clutchFoundedYear;
    private Boolean clutchVerificationBadge;
    private Boolean clutchVerified;
    private String clutchVerificationBadgeText;
    private String clutchAllServices;
    private String clutchAllIndustries;
    private String topMentions;
    private Boolean clutchStatus = false;
    
    // Goodfirms-related fields
    private Boolean goodfirmsStatus = false;
    private Double goodfirmsRating;
    private Integer goodfirmsReviewsCount;
    private String goodfirmsLocation;
    private String goodfirmsEmployees;
    private String goodfirmsServices;
    private String goodfirmsClientLikes;
    private String goodfirmsClientDislikes;
    private String goodfirmsCompanyLogoURL;
    private String goodfirmsCompanyOverview;
    private String goodfirmsCompanyUrl;
    private String goodfirmsProfileUrl;
    private String goodfirmsHourlyRate;
    private String goodfirmsMinProjectSize;
    private String goodfirmsFoundedYear;
    private String goodfirmsKeyClients;
    private String goodfirmsPortfolioItems;
    private String goodfirmsAwards;
    private String goodfirmsCertifications;
    private String goodfirmsAllServices;
    private String goodfirmsAllIndustries;
    
    // LinkedIn-related fields
    private Boolean linkedinStatus = false;
    private Integer linkedinFollowers;
    private Integer linkedinEmployeesCount;
    private String linkedinIndustry;
    private String linkedinCompanyLogoURL;
    private String linkedinCompanyOverview;
    private String linkedinCompanyUrl;
    private String linkedinLocation;
    private String linkedinFoundedYear;
    private String linkedinSpecialties;
    private String linkedinWebsite;
    
    // Website-related fields
    private Boolean websiteStatus = false;
    private String websiteName;
    private String websiteTitle;
    private String websiteDescription;
    private String websiteKeywords;
    private String websiteLogoURL;
    private String websiteContactEmail;
    private String websiteContactPhone;
    private String websiteAddress;
    private String websiteAboutUs;
    private String websiteServices;
    private String websiteIndustries;
    private String websitePortfolio;
    private String websiteTestimonials;
    private String websiteBlog;
    private String websiteCareers;
    private String websitePrivacyPolicy;
    private String websiteTermsOfService;
    private String websiteSocialMediaLinks;
    private String websiteTechnologies;
    private String websiteFeatures;
    private String websiteProductServices;
    
    // Glassdoor-related fields
    private Boolean glassdoorStatus = false;
    private Double glassdoorRating;
    private Integer glassdoorReviewsCount;
    private Double glassdoorEmployeeSatisfaction;
    private String glassdoorCategoryRatings; // JSON string of category ratings
    private String glassdoorRatingsDistribution; // JSON string of ratings distribution
    private String glassdoorPros; // JSON string of pros
    private String glassdoorCons; // JSON string of cons
    private String glassdoorCompanyLogoURL;
    private String glassdoorCompanyOverview;
    private String glassdoorCompanyUrl;
    private String glassdoorLocation;
    private String glassdoorEmployees;
    private String glassdoorIndustry;
    private String glassdoorFoundedYear;
    private String glassdoorRevenue;
    private String glassdoorCompetitors;
    private String glassdoorMission;
    private String glassdoorCEO;
    private String glassdoorCEORating;
    private String glassdoorCEOApproval;
    private String glassdoorRecommendToFriend;
    private String glassdoorCareerOpportunities;
    private String glassdoorCompensationAndBenefits;
    private String glassdoorCultureAndValues;
    private String glassdoorSeniorManagement;
    private String glassdoorWorkLifeBalance;
    private String glassdoorJobSecurity;
    private String glassdoorAdvancement;
    private String glassdoorManagement;
    private String glassdoorCulture;
    
    // Common fields
    private String city;
    private String country;
    private String industry;
    private String specialties;
    
    // Processing status
    private Double dataCompletenessScore;
    private LocalDateTime lastScraped;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
