import { BrowserRouter, Route, Routes } from "react-router-dom";
import { Toaster } from "sonner";
import "./App.css";
import { DashboardOverview } from "./components/dashboard/dashboard-overview";
import ScrapingJobs from "./components/dashboard/scraping-jobs";
import { RootLayout } from "./components/layout/root-layout";

function App() {
  return (
    <BrowserRouter>
      <RootLayout>
        <Routes>
          <Route path="/" element={<DashboardOverview />} />
          <Route path="/scraping" element={<ScrapingJobs />} />
        </Routes>
      </RootLayout>
      <Toaster />
    </BrowserRouter>
  );
}

export default App;
