package com.enosisbd.app.service.impl;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.enosisbd.app.model.Company;
import com.enosisbd.app.model.CompanyDataModel;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.service.ClutchScraperService;
import com.enosisbd.app.service.FileBasedJobService;
import com.enosisbd.app.service.GlassdoorScraperService;
import com.enosisbd.app.service.GoodfirmsScraperService;
import com.enosisbd.app.service.ScrapeSchedulerService;
import com.enosisbd.app.service.ScrapingExecutionService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of ScrapingExecutionService that coordinates the execution of all scrapers
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScrapingExecutionServiceImpl implements ScrapingExecutionService {

    private final ClutchScraperService clutchScraperService;
    private final GlassdoorScraperService glassdoorScraperService;
    private final GoodfirmsScraperService goodfirmsScraperService;
    private final FileBasedJobService fileBasedJobService;
    private final ApplicationContext applicationContext;
    
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Override
    public void executeScraping(ScrapeJob job) {
        log.info("Starting scraping execution for job {} (name: {})", job.getId(), job.getName());
        
        // Execute scraping asynchronously to avoid blocking the scheduler
        CompletableFuture.runAsync(() -> {
            try {
                performScraping(job);
            } catch (Exception e) {
                log.error("Error during scraping execution for job {}: {}", job.getId(), e.getMessage(), e);
                getScrapeSchedulerService().markAsFailed(job, "Scraping execution failed: " + e.getMessage());
            }
        }, executorService);
    }
    
    private void performScraping(ScrapeJob job) {
        log.info("Performing scraping for job {} (name: {})", job.getId(), job.getName());
        
        CompanyDataModel companyData = new CompanyDataModel();
        companyData.setName(job.getName());
        companyData.setScrapedAt(LocalDateTime.now());
        
        boolean hasAnySuccess = false;
        StringBuilder errorMessages = new StringBuilder();
        
        // Scrape from Clutch if URL is provided
        if (job.getClutch() != null && !job.getClutch().trim().isEmpty()) {
            try {
                log.info("Scraping Clutch data for job {} from URL: {}", job.getId(), job.getClutch());
                CompanyDataModel clutchData = clutchScraperService.scrapeFromClutch(job.getClutch());
                if (clutchData != null && Boolean.TRUE.equals(clutchData.getIsSuccessful())) {
                    mergeClutchData(companyData, clutchData);
                    hasAnySuccess = true;
                    log.info("Successfully scraped Clutch data for job {}", job.getId());
                } else {
                    log.warn("Clutch scraping failed for job {}", job.getId());
                    errorMessages.append("Clutch scraping failed; ");
                }
            } catch (Exception e) {
                log.error("Error scraping Clutch for job {}: {}", job.getId(), e.getMessage(), e);
                errorMessages.append("Clutch error: ").append(e.getMessage()).append("; ");
            }
        }
        
        // Scrape from Glassdoor if URL is provided
        if (job.getGlassdoor() != null && !job.getGlassdoor().trim().isEmpty()) {
            try {
                log.info("Scraping Glassdoor data for job {} from URL: {}", job.getId(), job.getGlassdoor());
                CompanyDataModel glassdoorData = glassdoorScraperService.scrapeFromGlassdoor(job.getGlassdoor());
                if (glassdoorData != null && Boolean.TRUE.equals(glassdoorData.getIsSuccessful())) {
                    mergeGlassdoorData(companyData, glassdoorData);
                    hasAnySuccess = true;
                    log.info("Successfully scraped Glassdoor data for job {}", job.getId());
                } else {
                    log.warn("Glassdoor scraping failed for job {}", job.getId());
                    errorMessages.append("Glassdoor scraping failed; ");
                }
            } catch (Exception e) {
                log.error("Error scraping Glassdoor for job {}: {}", job.getId(), e.getMessage(), e);
                errorMessages.append("Glassdoor error: ").append(e.getMessage()).append("; ");
            }
        }
        
        // Scrape from Goodfirms if URL is provided
        if (job.getGoodfirms() != null && !job.getGoodfirms().trim().isEmpty()) {
            try {
                log.info("Scraping Goodfirms data for job {} from URL: {}", job.getId(), job.getGoodfirms());
                CompanyDataModel goodfirmsData = goodfirmsScraperService.scrapeFromGoodFirms(job.getGoodfirms());
                if (goodfirmsData != null && Boolean.TRUE.equals(goodfirmsData.getIsSuccessful())) {
                    mergeGoodfirmsData(companyData, goodfirmsData);
                    hasAnySuccess = true;
                    log.info("Successfully scraped Goodfirms data for job {}", job.getId());
                } else {
                    log.warn("Goodfirms scraping failed for job {}", job.getId());
                    errorMessages.append("Goodfirms scraping failed; ");
                }
            } catch (Exception e) {
                log.error("Error scraping Goodfirms for job {}: {}", job.getId(), e.getMessage(), e);
                errorMessages.append("Goodfirms error: ").append(e.getMessage()).append("; ");
            }
        }
        
        // Save the scraped data and update job status
        if (hasAnySuccess) {
            try {
                // Convert CompanyDataModel to Company and save
                Company company = convertToCompany(companyData, job);
                String jobFileName = job.getCsvFileName() != null ? job.getCsvFileName() : job.getId();
                fileBasedJobService.saveCompanyData(company, jobFileName);
                getScrapeSchedulerService().markAsCompleted(job);
                log.info("Successfully completed scraping for job {} (name: {})", job.getId(), job.getName());
            } catch (Exception e) {
                log.error("Error saving scraped data for job {}: {}", job.getId(), e.getMessage(), e);
                getScrapeSchedulerService().markAsFailed(job, "Failed to save scraped data: " + e.getMessage());
            }
        } else {
            String errorMessage = errorMessages.length() > 0 ? errorMessages.toString() : "All scraping attempts failed";
            log.error("All scraping attempts failed for job {}: {}", job.getId(), errorMessage);
            getScrapeSchedulerService().markAsFailed(job, errorMessage);
        }
    }

    /**
     * Get ScrapeSchedulerService lazily to avoid circular dependency
     */
    private ScrapeSchedulerService getScrapeSchedulerService() {
        return applicationContext.getBean(ScrapeSchedulerService.class);
    }

    /**
     * Convert CompanyDataModel to Company entity
     */
    private Company convertToCompany(CompanyDataModel data, ScrapeJob job) {
        Company company = new Company();

        // Basic information
        company.setName(data.getName() != null ? data.getName() : job.getName());
        company.setCreatedAt(LocalDateTime.now());
        company.setUpdatedAt(LocalDateTime.now());

        // Clutch data
        if (data.getClutchRating() != null) company.setClutchRating(data.getClutchRating());
        if (data.getClutchReviewsCount() != null) company.setClutchReviewsCount(data.getClutchReviewsCount());
        if (data.getClutchLocation() != null) company.setClutchLocation(data.getClutchLocation());
        if (data.getClutchEmployees() != null) company.setClutchEmployees(data.getClutchEmployees());
        if (data.getClutchHourlyRate() != null) company.setClutchHourlyRate(data.getClutchHourlyRate());
        if (data.getClutchMinProjectSize() != null) company.setClutchMinProjectSize(data.getClutchMinProjectSize());
        if (data.getClutchFoundedYear() != null) company.setClutchFoundedYear(data.getClutchFoundedYear());
        if (data.getClutchVerificationBadge() != null) company.setClutchVerificationBadge(data.getClutchVerificationBadge());
        if (data.getClutchVerified() != null) company.setClutchVerified(data.getClutchVerified());
        if (data.getClutchCompanyLogoURL() != null) company.setClutchCompanyLogoURL(data.getClutchCompanyLogoURL());
        if (data.getClutchCompanyOverview() != null) company.setClutchCompanyOverview(data.getClutchCompanyOverview());
        if (data.getClutchCompanyUrl() != null) company.setClutchCompanyUrl(data.getClutchCompanyUrl());
        if (data.getClutchProfileUrl() != null) company.setClutchProfileUrl(data.getClutchProfileUrl());
        if (data.getClutchProviderName() != null) company.setClutchProviderName(data.getClutchProviderName());
        if (data.getClutchSlug() != null) company.setClutchSlug(data.getClutchSlug());

        // Glassdoor data
        if (data.getGlassdoorRating() != null) company.setGlassdoorRating(data.getGlassdoorRating());
        if (data.getGlassdoorReviewsCount() != null) company.setGlassdoorReviewsCount(data.getGlassdoorReviewsCount());
        if (data.getGlassdoorEmployeeSatisfaction() != null) company.setGlassdoorEmployeeSatisfaction(data.getGlassdoorEmployeeSatisfaction());

        // Goodfirms data
        if (data.getGoodfirmsRating() != null) company.setGoodfirmsRating(data.getGoodfirmsRating());
        if (data.getGoodfirmsReviewsCount() != null) company.setGoodfirmsReviewsCount(data.getGoodfirmsReviewsCount());
        if (data.getGoodfirmsLocation() != null) company.setGoodfirmsLocation(data.getGoodfirmsLocation());
        if (data.getGoodfirmsEmployees() != null) company.setGoodfirmsEmployees(data.getGoodfirmsEmployees());
        if (data.getGoodfirmsServices() != null) company.setGoodfirmsServices(data.getGoodfirmsServices());

        // Note: CSV metadata fields don't exist in Company model

        return company;
    }

    private void mergeClutchData(CompanyDataModel target, CompanyDataModel source) {
        // Merge Clutch-specific data
        if (source.getClutchRating() != null) target.setClutchRating(source.getClutchRating());
        if (source.getClutchReviewsCount() != null) target.setClutchReviewsCount(source.getClutchReviewsCount());
        if (source.getClutchLocation() != null) target.setClutchLocation(source.getClutchLocation());
        if (source.getClutchMinProjectSize() != null) target.setClutchMinProjectSize(source.getClutchMinProjectSize());
        if (source.getClutchHourlyRate() != null) target.setClutchHourlyRate(source.getClutchHourlyRate());
        if (source.getClutchEmployees() != null) target.setClutchEmployees(source.getClutchEmployees());
        if (source.getClutchFoundedYear() != null) target.setClutchFoundedYear(source.getClutchFoundedYear());
        if (source.getClutchAllServices() != null) target.setClutchAllServices(source.getClutchAllServices());
        if (source.getClutchAllIndustries() != null) target.setClutchAllIndustries(source.getClutchAllIndustries());
    }
    
    private void mergeGlassdoorData(CompanyDataModel target, CompanyDataModel source) {
        // Merge Glassdoor-specific data
        if (source.getGlassdoorRating() != null) target.setGlassdoorRating(source.getGlassdoorRating());
        if (source.getGlassdoorReviewsCount() != null) target.setGlassdoorReviewsCount(source.getGlassdoorReviewsCount());
        if (source.getGlassdoorEmployeeSatisfaction() != null) target.setGlassdoorEmployeeSatisfaction(source.getGlassdoorEmployeeSatisfaction());
        if (source.getCategoryRatings() != null) target.setCategoryRatings(source.getCategoryRatings());
        if (source.getRatingsDistribution() != null) target.setRatingsDistribution(source.getRatingsDistribution());
        if (source.getPros() != null) target.setPros(source.getPros());
        if (source.getCons() != null) target.setCons(source.getCons());
    }
    
    private void mergeGoodfirmsData(CompanyDataModel target, CompanyDataModel source) {
        // Merge Goodfirms-specific data
        if (source.getGoodfirmsRating() != null) target.setGoodfirmsRating(source.getGoodfirmsRating());
        if (source.getGoodfirmsReviewsCount() != null) target.setGoodfirmsReviewsCount(source.getGoodfirmsReviewsCount());
        if (source.getGoodfirmVerificationBadge() != null) target.setGoodfirmVerificationBadge(source.getGoodfirmVerificationBadge());
        if (source.getGoodfirmsLocation() != null) target.setGoodfirmsLocation(source.getGoodfirmsLocation());
        if (source.getGoodfirmsEmployees() != null) target.setGoodfirmsEmployees(source.getGoodfirmsEmployees());
        if (source.getGoodfirmsServices() != null) target.setGoodfirmsServices(source.getGoodfirmsServices());
        if (source.getGoodfirmProviderName() != null) target.setGoodfirmProviderName(source.getGoodfirmProviderName());
        if (source.getGoodfirmRatings() != null) target.setGoodfirmRatings(source.getGoodfirmRatings());
        if (source.getGoodfirmProfileUrl() != null) target.setGoodfirmProfileUrl(source.getGoodfirmProfileUrl());
        if (source.getClientLikes() != null) target.setClientLikes(source.getClientLikes());
        if (source.getClientDislikes() != null) target.setClientDislikes(source.getClientDislikes());
    }
}
