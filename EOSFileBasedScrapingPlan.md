# EOS Data Scraper Workflow (File-Based)

This document outlines the revised data scraping workflow for the EOS Tools application, which is now based on a file system rather than a database.

## 1. Workflow Overview

The data scraping process is initiated when a user uploads a file containing a list of companies. The backend processes this file, scrapes the data, and stores the results as JSON files. The input file is then moved to a processed directory to keep track of completed jobs.

The entire workflow is as follows:

1.  **File Upload:** The user uploads a file, which is saved directly to the `backend/input/` folder.
2.  **Processing:** A file watcher in the backend detects new files in the `input` directory and begins processing them. The file remains in the `input` folder during processing.
3.  **Data Scraping:** The system scrapes data from various sources, including Clutch, GoodFirms, and the company's website.
4.  **Output Generation:** For each company scraped, a corresponding JSON file is created in the `backend/output/` directory.
5.  **File Archiving:** Once all the companies in the input file have been processed, the input file is moved from `backend/input/` to the `backend/processed/` directory. This signifies the completion of the scraping job.

## 2. R&D and UI/UX Enhancements

To support this new file-based workflow, the user interface will be updated to provide a more intuitive experience.

### 2.1. Dashboard Redesign

The main dashboard will be divided into two sections:

*   **Running Scraping Jobs:** This section will display a list of files currently in the `backend/input/` folder. This gives the user a real-time view of the jobs that are currently being processed.
*   **Completed Scraping Jobs:** This section will show a list of files from the `backend/processed/` folder. This serves as a history of all completed scraping jobs.

### 2.2. Job Management

*   **Viewing Results:** When a user clicks on a job in the "Completed Scraping Jobs" list, the frontend will fetch and display the data from the corresponding JSON files in the `backend/output/` folder.
*   **Deleting Jobs:** Each completed job will have a "Delete" button. Clicking this will trigger a request to the backend to delete the input file from the `processed` folder and all associated output files from the `output` folder.

## 3. Implementation Plan

Here is a detailed plan to implement the new file-based workflow:

### 3.1. Backend Changes

1.  **Remove Database Dependencies:**
    *   Delete all JPA entities from `src/main/java/com/enosisbd/app/entity`.
    *   Remove the corresponding repositories from `src/main/java/com/enosisbd/app/repository`.
    *   Delete `JpaConfig` and any other database-related configuration.
    *   Update `pom.xml` to remove the Spring Data JPA and database driver dependencies.

2.  **Update File Processing Logic:**
    *   Modify the existing file watcher or scheduler.
    *   After a file is fully processed, implement logic to move it from the `input` directory to the `processed` directory.

3.  **Create New REST APIs:**
    *   **`GET /api/jobs/running`**: Returns a list of filenames from the `backend/input/` directory.
    *   **`GET /api/jobs/completed`**: Returns a list of filenames from the `backend/processed/` directory.
    *   **`GET /api/jobs/completed/{filename}`**: Reads the corresponding output JSON files from the `backend/output/` directory and returns their content. The backend will need to determine which output files correspond to the given input `filename`.
    *   **`DELETE /api/jobs/completed/{filename}`**: Deletes the specified file from the `backend/processed/` directory and all associated output files from the `backend/output/` directory.

### 3.2. Frontend Changes

1.  **Update `scraping-jobs.tsx`:**
    *   Redesign the component to have two distinct sections: "Running Scraping Jobs" and "Completed Scraping Jobs".

2.  **Implement API Calls:**
    *   On component mount, call the new `GET` endpoints to populate the "Running" and "Completed" job lists.
    *   Implement polling for the "Running" jobs section to provide status updates.

3.  **Display Job Results:**
    *   When a user clicks on a completed job, make an API call to `GET /api/jobs/completed/{filename}`.
    *   Render the fetched JSON data in a user-friendly format (e.g., a table or a series of cards).

4.  **Implement Delete Functionality:**
    *   Add a delete button to each item in the "Completed Scraping Jobs" list.
    *   When clicked, call the `DELETE /api/jobs/completed/{filename}` endpoint.
    *   On a successful response, remove the job from the list in the UI.