package com.enosisbd.app.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.enosisbd.app.model.Company;
import com.enosisbd.app.model.JobFile;
import com.enosisbd.app.service.FileBasedJobService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * REST controller for file-based job management
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/jobs")
public class JobController {

    private final FileBasedJobService fileBasedJobService;

    /**
     * Get all running jobs (files currently being processed)
     * @return List of running job files
     */
    @GetMapping("/running")
    public ResponseEntity<List<JobFile>> getRunningJobs() {
        log.info("Fetching running jobs");
        List<JobFile> runningJobs = fileBasedJobService.getRunningJobs();
        return ResponseEntity.ok(runningJobs);
    }

    /**
     * Get all completed jobs (files that have been processed)
     * @return List of completed job files
     */
    @GetMapping("/completed")
    public ResponseEntity<List<JobFile>> getCompletedJobs() {
        log.info("Fetching completed jobs");
        List<JobFile> completedJobs = fileBasedJobService.getCompletedJobs();
        return ResponseEntity.ok(completedJobs);
    }

    /**
     * Get results for a specific completed job
     * @param filename The name of the completed job file
     * @return List of companies scraped for this job
     */
    @GetMapping("/completed/{filename}")
    public ResponseEntity<List<Company>> getJobResults(@PathVariable String filename) {
        log.info("Fetching results for job: {}", filename);
        List<Company> results = fileBasedJobService.getJobResults(filename);
        
        if (results.isEmpty()) {
            log.warn("No results found for job: {}", filename);
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(results);
    }

    /**
     * Delete a completed job and all its associated files
     * @param filename The name of the completed job file to delete
     * @return Success or error response
     */
    @DeleteMapping("/completed/{filename}")
    public ResponseEntity<Void> deleteCompletedJob(@PathVariable String filename) {
        log.info("Deleting completed job: {}", filename);
        
        boolean deleted = fileBasedJobService.deleteCompletedJob(filename);
        
        if (deleted) {
            log.info("Successfully deleted job: {}", filename);
            return ResponseEntity.noContent().build();
        } else {
            log.warn("Failed to delete job or job not found: {}", filename);
            return ResponseEntity.notFound().build();
        }
    }
}
