package com.enosisbd.app.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for file-based job storage
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "file-based-job")
public class FileBasedJobProperties {
    
    /**
     * Directory where job files are stored
     */
    private String jobsFolder = "./jobs";
    
    /**
     * Directory where company data files are stored
     */
    private String companiesFolder = "./companies";
    
    /**
     * Whether to automatically create folders if they don't exist
     */
    private boolean autoCreateFolders = true;
}
