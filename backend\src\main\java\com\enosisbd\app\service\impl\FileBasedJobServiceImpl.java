package com.enosisbd.app.service.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.enosisbd.app.config.FileBasedJobProperties;
import com.enosisbd.app.config.FileWatcherProperties;
import com.enosisbd.app.model.Company;
import com.enosisbd.app.model.JobFile;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.model.ScrapeStatus;
import com.enosisbd.app.service.FileBasedJobService;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * File-based implementation of job service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileBasedJobServiceImpl implements FileBasedJobService {
    
    private final ObjectMapper objectMapper;
    private final FileBasedJobProperties fileBasedJobProperties;
    private final FileWatcherProperties fileWatcherProperties;
    
    private static final String COMPANIES_SUBFOLDER = "companies";
    private static final String JOB_INFO_SUBFOLDER = "job-info";
    
    @Override
    public List<JobFile> getRunningJobs() {
        try {
            Path inputPath = Paths.get(fileWatcherProperties.getInputFolder());
            if (!Files.exists(inputPath)) {
                return new ArrayList<>();
            }

            return Files.list(inputPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json") || path.toString().endsWith(".csv"))
                    .map(this::createJobFileFromPath)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (IOException e) {
            log.error("Error reading running jobs", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<JobFile> getCompletedJobs() {
        try {
            Path processedPath = Paths.get(fileWatcherProperties.getProcessedFolder());
            if (!Files.exists(processedPath)) {
                return new ArrayList<>();
            }

            return Files.list(processedPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json") || path.toString().endsWith(".csv"))
                    .map(this::createJobFileFromPath)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (IOException e) {
            log.error("Error reading completed jobs", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public List<Company> getJobResults(String fileName) {
        try {
            // Look for company files in output folder that match the job file name
            Path outputPath = Paths.get(fileWatcherProperties.getOutputFolder());
            if (!Files.exists(outputPath)) {
                return new ArrayList<>();
            }
            
            String baseFileName = getBaseFileName(fileName);
            List<Company> companies = new ArrayList<>();
            
            Files.list(outputPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.getFileName().toString().startsWith(baseFileName + "_"))
                    .filter(path -> path.toString().endsWith(".json"))
                    .forEach(path -> {
                        try {
                            Company company = objectMapper.readValue(path.toFile(), Company.class);
                            companies.add(company);
                        } catch (IOException e) {
                            log.error("Error reading company file: {}", path, e);
                        }
                    });
            
            return companies;
            
        } catch (IOException e) {
            log.error("Error getting job results for: {}", fileName, e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public boolean deleteCompletedJob(String fileName) {
        try {
            String baseFileName = getBaseFileName(fileName);

            // Delete the processed file
            Path processedFile = Paths.get(fileWatcherProperties.getProcessedFolder(), fileName);
            if (Files.exists(processedFile)) {
                Files.delete(processedFile);
            }

            // Delete associated output files
            Path outputPath = Paths.get(fileWatcherProperties.getOutputFolder());
            if (Files.exists(outputPath)) {
                Files.list(outputPath)
                        .filter(Files::isRegularFile)
                        .filter(path -> path.getFileName().toString().startsWith(baseFileName + "_"))
                        .forEach(path -> {
                            try {
                                Files.delete(path);
                            } catch (IOException e) {
                                log.error("Error deleting output file: {}", path, e);
                            }
                        });
            }

            // Delete job info file
            Path jobInfoFile = Paths.get(fileBasedJobProperties.getJobsFolder(), JOB_INFO_SUBFOLDER, baseFileName + ".json");
            if (Files.exists(jobInfoFile)) {
                Files.delete(jobInfoFile);
            }

            return true;

        } catch (IOException e) {
            log.error("Error deleting completed job: {}", fileName, e);
            return false;
        }
    }
    
    private JobFile createJobFileFromPath(Path path) {
        try {
            String fileName = path.getFileName().toString();
            JobFile jobFile = new JobFile();
            jobFile.setFileName(fileName);
            jobFile.setFilePath(path.toString());
            
            // Try to get additional info from job info file
            String baseFileName = getBaseFileName(fileName);
            Path jobInfoFile = Paths.get(fileBasedJobProperties.getJobsFolder(), JOB_INFO_SUBFOLDER, baseFileName + ".json");
            if (Files.exists(jobInfoFile)) {
                JobFile savedJobFile = objectMapper.readValue(jobInfoFile.toFile(), JobFile.class);
                return savedJobFile;
            } else {
                // Create basic info from file attributes
                jobFile.setUploadedAt(LocalDateTime.now());
                jobFile.setStatus(path.toString().contains(fileWatcherProperties.getProcessedFolder()) ?
                    JobFile.JobFileStatus.COMPLETED : JobFile.JobFileStatus.PROCESSING);
                return jobFile;
            }
            
        } catch (Exception e) {
            log.error("Error creating job file from path: {}", path, e);
            return null;
        }
    }
    
    private String getBaseFileName(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        return lastDot > 0 ? fileName.substring(0, lastDot) : fileName;
    }

    @Override
    public void saveScrapeJob(ScrapeJob scrapeJob) {
        try {
            ensureDirectoryExists(Paths.get(fileBasedJobProperties.getJobsFolder()));

            if (scrapeJob.getId() == null) {
                scrapeJob.setId(UUID.randomUUID().toString());
            }

            if (scrapeJob.getCreatedAt() == null) {
                scrapeJob.setCreatedAt(LocalDateTime.now());
            }
            scrapeJob.setUpdatedAt(LocalDateTime.now());

            Path jobFile = Paths.get(fileBasedJobProperties.getJobsFolder(), scrapeJob.getId() + ".json");
            objectMapper.writeValue(jobFile.toFile(), scrapeJob);

        } catch (IOException e) {
            log.error("Error saving scrape job: {}", scrapeJob.getId(), e);
            throw new RuntimeException("Failed to save scrape job", e);
        }
    }

    @Override
    public List<ScrapeJob> getScrapeJobsByStatus(ScrapeStatus status) {
        try {
            Path jobsPath = Paths.get(fileBasedJobProperties.getJobsFolder());
            if (!Files.exists(jobsPath)) {
                return new ArrayList<>();
            }

            return Files.list(jobsPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json"))
                    .map(this::readScrapeJobFromFile)
                    .filter(Objects::nonNull)
                    .filter(job -> job.getStatus() == status)
                    .collect(Collectors.toList());

        } catch (IOException e) {
            log.error("Error getting scrape jobs by status: {}", status, e);
            return new ArrayList<>();
        }
    }

    @Override
    public void updateScrapeJobStatus(String jobId, ScrapeStatus status) {
        Optional<ScrapeJob> jobOpt = getScrapeJobById(jobId);
        if (jobOpt.isPresent()) {
            ScrapeJob job = jobOpt.get();
            job.setStatus(status);
            job.setUpdatedAt(LocalDateTime.now());

            if (status == ScrapeStatus.IN_PROGRESS) {
                job.setLastProcessedTime(LocalDateTime.now());
            }

            saveScrapeJob(job);
        }
    }

    @Override
    public Optional<ScrapeJob> getScrapeJobById(String jobId) {
        try {
            Path jobFile = Paths.get(fileBasedJobProperties.getJobsFolder(), jobId + ".json");
            if (Files.exists(jobFile)) {
                ScrapeJob job = objectMapper.readValue(jobFile.toFile(), ScrapeJob.class);
                return Optional.of(job);
            }
            return Optional.empty();

        } catch (IOException e) {
            log.error("Error getting scrape job by ID: {}", jobId, e);
            return Optional.empty();
        }
    }

    @Override
    public void saveCompanyData(Company company, String jobFileName) {
        try {
            ensureDirectoryExists(Paths.get(fileWatcherProperties.getOutputFolder()));

            if (company.getId() == null) {
                company.setId(UUID.randomUUID().toString());
            }

            if (company.getCreatedAt() == null) {
                company.setCreatedAt(LocalDateTime.now());
            }
            company.setUpdatedAt(LocalDateTime.now());

            String baseFileName = getBaseFileName(jobFileName);
            String outputFileName = baseFileName + "_" + company.getId() + ".json";
            Path outputFile = Paths.get(fileWatcherProperties.getOutputFolder(), outputFileName);

            objectMapper.writeValue(outputFile.toFile(), company);

        } catch (IOException e) {
            log.error("Error saving company data for job: {}", jobFileName, e);
            throw new RuntimeException("Failed to save company data", e);
        }
    }

    @Override
    public Optional<Company> getCompanyById(String companyId) {
        try {
            Path outputPath = Paths.get(fileWatcherProperties.getOutputFolder());
            if (!Files.exists(outputPath)) {
                return Optional.empty();
            }

            Optional<Path> companyFile = Files.list(outputPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith("_" + companyId + ".json"))
                    .findFirst();

            if (companyFile.isPresent()) {
                Company company = objectMapper.readValue(companyFile.get().toFile(), Company.class);
                return Optional.of(company);
            }

            return Optional.empty();

        } catch (IOException e) {
            log.error("Error getting company by ID: {}", companyId, e);
            return Optional.empty();
        }
    }

    @Override
    public void saveCompany(Company company) {
        // For file-based approach, we save companies as part of job results
        // This method can be used for standalone company saves if needed
        try {
            ensureDirectoryExists(Paths.get(fileWatcherProperties.getOutputFolder()));

            if (company.getId() == null) {
                company.setId(UUID.randomUUID().toString());
            }

            if (company.getCreatedAt() == null) {
                company.setCreatedAt(LocalDateTime.now());
            }
            company.setUpdatedAt(LocalDateTime.now());

            String outputFileName = "standalone_" + company.getId() + ".json";
            Path outputFile = Paths.get(fileWatcherProperties.getOutputFolder(), outputFileName);

            objectMapper.writeValue(outputFile.toFile(), company);

        } catch (IOException e) {
            log.error("Error saving company: {}", company.getId(), e);
            throw new RuntimeException("Failed to save company", e);
        }
    }

    @Override
    public List<Company> getCompaniesByJobFile(String jobFileName) {
        return getJobResults(jobFileName);
    }

    @Override
    public void moveFileToProcessed(String fileName) {
        try {
            Path sourceFile = Paths.get(fileWatcherProperties.getInputFolder(), fileName);
            Path targetFile = Paths.get(fileWatcherProperties.getProcessedFolder(), fileName);

            ensureDirectoryExists(Paths.get(fileWatcherProperties.getProcessedFolder()));

            if (Files.exists(sourceFile)) {
                Files.move(sourceFile, targetFile, StandardCopyOption.REPLACE_EXISTING);
                log.info("Moved file from input to processed: {}", fileName);
            }

        } catch (IOException e) {
            log.error("Error moving file to processed: {}", fileName, e);
            throw new RuntimeException("Failed to move file to processed", e);
        }
    }

    @Override
    public Optional<JobFile> getJobFileInfo(String fileName) {
        try {
            String baseFileName = getBaseFileName(fileName);
            Path jobInfoFile = Paths.get(fileBasedJobProperties.getJobsFolder(), JOB_INFO_SUBFOLDER, baseFileName + ".json");

            if (Files.exists(jobInfoFile)) {
                JobFile jobFile = objectMapper.readValue(jobInfoFile.toFile(), JobFile.class);
                return Optional.of(jobFile);
            }

            return Optional.empty();

        } catch (IOException e) {
            log.error("Error getting job file info: {}", fileName, e);
            return Optional.empty();
        }
    }

    @Override
    public void updateJobFileInfo(JobFile jobFile) {
        try {
            ensureDirectoryExists(Paths.get(fileBasedJobProperties.getJobsFolder(), JOB_INFO_SUBFOLDER));

            String baseFileName = getBaseFileName(jobFile.getFileName());
            Path jobInfoFile = Paths.get(fileBasedJobProperties.getJobsFolder(), JOB_INFO_SUBFOLDER, baseFileName + ".json");

            objectMapper.writeValue(jobInfoFile.toFile(), jobFile);

        } catch (IOException e) {
            log.error("Error updating job file info: {}", jobFile.getFileName(), e);
            throw new RuntimeException("Failed to update job file info", e);
        }
    }

    @Override
    public List<ScrapeJob> getItemsReadyForScheduling() {
        LocalDateTime currentTime = LocalDateTime.now();

        return getScrapeJobsByStatus(ScrapeStatus.QUEUE).stream()
                .filter(job -> job.getNextScheduledTime() == null ||
                              job.getNextScheduledTime().isBefore(currentTime) ||
                              job.getNextScheduledTime().isEqual(currentTime))
                .sorted(Comparator.comparing(ScrapeJob::getCreatedAt))
                .collect(Collectors.toList());
    }

    @Override
    public List<ScrapeJob> getItemsReadyForProcessing() {
        LocalDateTime currentTime = LocalDateTime.now();

        return getScrapeJobsByStatus(ScrapeStatus.SCHEDULED).stream()
                .filter(job -> job.getNextScheduledTime() != null &&
                              (job.getNextScheduledTime().isBefore(currentTime) ||
                               job.getNextScheduledTime().isEqual(currentTime)))
                .sorted(Comparator.comparing(ScrapeJob::getNextScheduledTime))
                .collect(Collectors.toList());
    }

    @Override
    public Optional<ScrapeJob> getMostRecentlyProcessedItem() {
        try {
            Path jobsPath = Paths.get(fileBasedJobProperties.getJobsFolder());
            if (!Files.exists(jobsPath)) {
                return Optional.empty();
            }

            return Files.list(jobsPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json"))
                    .map(this::readScrapeJobFromFile)
                    .filter(Objects::nonNull)
                    .filter(job -> job.getLastProcessedTime() != null)
                    .max(Comparator.comparing(ScrapeJob::getLastProcessedTime));

        } catch (IOException e) {
            log.error("Error getting most recently processed item", e);
            return Optional.empty();
        }
    }

    @Override
    public List<ScrapeJob> getScrapeJobsByCsvUploadId(String csvUploadId) {
        try {
            Path jobsPath = Paths.get(fileBasedJobProperties.getJobsFolder());
            if (!Files.exists(jobsPath)) {
                return new ArrayList<>();
            }

            return Files.list(jobsPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json"))
                    .map(this::readScrapeJobFromFile)
                    .filter(Objects::nonNull)
                    .filter(job -> csvUploadId.equals(job.getCsvUploadId()))
                    .collect(Collectors.toList());

        } catch (IOException e) {
            log.error("Error getting scrape jobs by CSV upload ID: {}", csvUploadId, e);
            return new ArrayList<>();
        }
    }

    private ScrapeJob readScrapeJobFromFile(Path path) {
        try {
            return objectMapper.readValue(path.toFile(), ScrapeJob.class);
        } catch (IOException e) {
            log.error("Error reading scrape job from file: {}", path, e);
            return null;
        }
    }

    @Override
    public List<Company> getAllCompanies() {
        try {
            Path outputPath = Paths.get(fileWatcherProperties.getOutputFolder());
            if (!Files.exists(outputPath)) {
                return new ArrayList<>();
            }

            List<Company> companies = new ArrayList<>();
            Files.list(outputPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json"))
                    .forEach(path -> {
                        try {
                            Company company = objectMapper.readValue(path.toFile(), Company.class);
                            companies.add(company);
                        } catch (IOException e) {
                            log.error("Error reading company file: {}", path, e);
                        }
                    });

            return companies;

        } catch (IOException e) {
            log.error("Error getting all companies", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getCsvUploadStatistics() {
        try {
            Path jobsPath = Paths.get(fileBasedJobProperties.getJobsFolder());
            if (!Files.exists(jobsPath)) {
                return new ArrayList<>();
            }

            Map<String, Map<String, Object>> uploadStats = new HashMap<>();

            Files.list(jobsPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json"))
                    .map(this::readScrapeJobFromFile)
                    .filter(Objects::nonNull)
                    .filter(job -> job.getCsvUploadId() != null)
                    .forEach(job -> {
                        String uploadId = job.getCsvUploadId();
                        uploadStats.computeIfAbsent(uploadId, k -> {
                            Map<String, Object> stats = new HashMap<>();
                            stats.put("csvUploadId", uploadId);
                            stats.put("csvFileName", job.getCsvFileName());
                            stats.put("totalJobs", 0);
                            stats.put("completedJobs", 0);
                            stats.put("failedJobs", 0);
                            stats.put("queuedJobs", 0);
                            stats.put("processingJobs", 0);
                            stats.put("uploadedAt", job.getCreatedAt());
                            return stats;
                        });

                        Map<String, Object> stats = uploadStats.get(uploadId);
                        stats.put("totalJobs", (Integer) stats.get("totalJobs") + 1);

                        switch (job.getStatus()) {
                            case SUCCESS:
                                stats.put("completedJobs", (Integer) stats.get("completedJobs") + 1);
                                break;
                            case FAILED:
                                stats.put("failedJobs", (Integer) stats.get("failedJobs") + 1);
                                break;
                            case QUEUE:
                                stats.put("queuedJobs", (Integer) stats.get("queuedJobs") + 1);
                                break;
                            case IN_PROGRESS:
                                stats.put("processingJobs", (Integer) stats.get("processingJobs") + 1);
                                break;
                            case SCHEDULED:
                                stats.put("queuedJobs", (Integer) stats.get("queuedJobs") + 1);
                                break;
                        }
                    });

            return new ArrayList<>(uploadStats.values());

        } catch (IOException e) {
            log.error("Error getting CSV upload statistics", e);
            return new ArrayList<>();
        }
    }

    private void ensureDirectoryExists(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            Files.createDirectories(directory);
        }
    }
}
