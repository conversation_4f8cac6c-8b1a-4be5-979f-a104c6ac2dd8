spring:
  main:
    banner-mode: OFF
  ai:
    ollama:
      base-url: http://192.168.0.252:11434
  profiles:
    active:
      - dev
  application:
    name: EOS Data Scraper
  # Database configuration removed - using file-based storage

# Scraping Scheduler Configuration (Fast settings for testing)
scraping:
  # Scraping timeout configuration
  timeout-ms: 120000

  # Goodfirms-specific configuration
  goodfirms:
    login-required: false
    # Specific timeouts for different Goodfirms operations
    basic-data-timeout-ms: 30000
    logo-timeout-ms: 15000
    feedback-timeout-ms: 45000
    page-load-timeout-ms: 20000

  # Glassdoor-specific configuration
  glassdoor:
    login-required: false
    # Specific timeouts for different Glassdoor operations
    page-load-timeout-ms: 30000
    ratings-timeout-ms: 20000
    pros-cons-timeout-ms: 25000
    overall-data-timeout-ms: 35000

  # Anti-detection configuration
  anti-detection:
    enabled: true
    random-delays: true
    min-delay-ms: 500
    max-delay-ms: 3000

  # General scraping timeout
  timeout: 30000

  # Retry configuration
  retry:
    max-attempts: 3
    backoff-multiplier: 2000

  # Page wait times
  wait-times:
    default-wait-ms: 5000
    review-section-wait-ms: 10000
    javascript-load-wait-ms: 8000

  scheduler:
    # Minimum gap between scraping operations in minutes (reduced for testing)
    min-gap-minutes: 1
    # Maximum additional random minutes to add to the minimum gap (reduced for testing)
    max-additional-random-minutes: 2
    # Maximum number of concurrent scraping operations
    max-concurrent-processing: 1
    # Default maximum retry attempts for failed scraping operations
    default-max-retries: 3
    # Job scheduling intervals
    scheduling-job-interval-minutes: 1
    processing-job-interval-minutes: 1
    maintenance-job-interval-minutes: 5
    # Initial delays for jobs
    scheduling-job-initial-delay-minutes: 1
    processing-job-initial-delay-minutes: 1
    maintenance-job-initial-delay-minutes: 1

# Flowable configuration removed - using file-based job management

# File-based Job Storage Configuration
file-based-job:
  jobs-folder: ${FILE_BASED_JOB_JOBS_FOLDER:./jobs}
  companies-folder: ${FILE_BASED_JOB_COMPANIES_FOLDER:./companies}
  auto-create-folders: true

# File Watcher Configuration
file-watcher:
  input-folder: ${FILE_WATCHER_INPUT_FOLDER:./input}
  output-folder: ${FILE_WATCHER_OUTPUT_FOLDER:./output}
  processed-folder: ${FILE_WATCHER_PROCESSED_FOLDER:./processed}
  error-folder: ${FILE_WATCHER_ERROR_FOLDER:./error}
  polling-interval-seconds: 5
  enabled: true
  supported-extensions:
    - json
  auto-create-folders: true
  # Processing configuration
  processing-timeout-minutes: 1
  large-file-threshold-mb: 5
  disable-auto-commit-for-large-files: true
  max-concurrent-files: 3

# Logging Configuration
logging:
  level:
    com.enosisbd.app: DEBUG
    org.flowable: INFO
    org.springframework.scheduling: DEBUG
