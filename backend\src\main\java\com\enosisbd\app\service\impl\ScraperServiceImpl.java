package com.enosisbd.app.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.model.Company;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.model.ScrapeStatus;
import com.enosisbd.app.service.FileBasedJobService;
import com.enosisbd.app.service.ScrapeSchedulerService;
import com.enosisbd.app.service.ScraperService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScraperServiceImpl implements ScraperService {

    private final FileBasedJobService fileBasedJobService;
    private final ScrapeSchedulerService scrapeSchedulerService;

    @Override
    public void saveScrapeRequest(ScrapeRequest scrapeRequest) {
        log.info("Saving scrape request for: {}", scrapeRequest.getName());

        // Create scrape job from request
        ScrapeJob scrapeJob = createScrapeJobFromRequest(scrapeRequest);

        // Calculate initial scheduled time with some randomization
        scrapeSchedulerService.calculateAndSetNextScheduledTime(scrapeJob);

        // Save the scrape job
        fileBasedJobService.saveScrapeJob(scrapeJob);

        log.info("Scrape request saved with ID: {} for company: {} and scheduled for: {}",
                scrapeJob.getId(), scrapeJob.getName(), scrapeJob.getNextScheduledTime());
    }

    /**
     * Create ScrapeJob from ScrapeRequest
     */
    private ScrapeJob createScrapeJobFromRequest(ScrapeRequest scrapeRequest) {
        ScrapeJob scrapeJob = new ScrapeJob();

        // Map fields from request to job
        scrapeJob.setName(scrapeRequest.getName());
        scrapeJob.setGlassdoor(scrapeRequest.getGlassdoor());
        scrapeJob.setClutch(scrapeRequest.getClutch());
        scrapeJob.setGoodfirms(scrapeRequest.getGoodfirms());
        scrapeJob.setWebsite(scrapeRequest.getWebsite());
        scrapeJob.setLinkedin(scrapeRequest.getLinkedin());
        scrapeJob.setStatus(ScrapeStatus.QUEUE);
        scrapeJob.setCsvFileName(scrapeRequest.getCsvFileName());
        scrapeJob.setCsvUploadId(scrapeRequest.getCsvUploadId());

        // Set initial status and scheduling info
        scrapeJob.setRetryCount(0);
        scrapeJob.setMaxRetries(3);
        scrapeJob.setCreatedAt(LocalDateTime.now());

        return scrapeJob;
    }

    @Override
    public List<ScrapeJob> getAllQueuedScrapes() {
        log.info("Fetching all queued scrape jobs");
        return fileBasedJobService.getScrapeJobsByStatus(ScrapeStatus.QUEUE);
    }

    @Override
    public List<ScrapeJob> getAllScrapesByStatus(ScrapeStatus status) {
        log.info("Fetching all scrape jobs with status: {}", status);
        return fileBasedJobService.getScrapeJobsByStatus(status);
    }

    @Override
    public List<Company> getAllCompanies() {
        log.info("Fetching all companies");
        return fileBasedJobService.getAllCompanies();
    }

    @Override
    public Optional<Company> getCompanyById(String id) {
        log.info("Fetching company with ID: {}", id);
        return fileBasedJobService.getCompanyById(id);
    }

    @Override
    public List<Company> getCompaniesByCompleteness(Double minScore) {
        log.info("Fetching companies with completeness score >= {}", minScore);
        // For now, return all companies - can implement filtering later
        return fileBasedJobService.getAllCompanies();
    }

    @Override
    public List<Map<String, Object>> getCsvUploads() {
        log.info("Fetching CSV upload statistics");
        return fileBasedJobService.getCsvUploadStatistics();
    }

    @Override
    public List<ScrapeJob> getScrapesByCsvUploadId(String csvUploadId) {
        log.info("Fetching scrape jobs for CSV upload ID: {}", csvUploadId);
        return fileBasedJobService.getScrapeJobsByCsvUploadId(csvUploadId);
    }
}
