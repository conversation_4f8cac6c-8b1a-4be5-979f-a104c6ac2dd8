package com.enosisbd.app.controller;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.enosisbd.app.config.FileWatcherProperties;
import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.model.Company;
import com.enosisbd.app.model.ScrapeJob;
import com.enosisbd.app.model.ScrapeRequestBatch;
import com.enosisbd.app.model.ScrapeStatus;
import com.enosisbd.app.service.ScraperService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import com.opencsv.exceptions.CsvValidationException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/scrapping")
public class ScraperController implements ScraperApi {

    private final ScraperService scraperService;
    private final ObjectMapper objectMapper;
    private final FileWatcherProperties fileWatcherProperties;

    @Override
    @PostMapping
    public ResponseEntity<?> scrape(@RequestBody ScrapeRequest scrapeRequest) {
        scraperService.saveScrapeRequest(scrapeRequest);
        return ResponseEntity.accepted().body(Map.of("message","Scrape Request Started!!"));
    }

    @GetMapping("/convert-json-to-csv")
    public ResponseEntity<byte[]> convertJsonToCsv() {
        try {
            // Read the JSON file
            InputStream inputStream = getClass().getResourceAsStream("/enosis.json");
            List<ScrapeRequest> requests = objectMapper.readValue(
                inputStream,
                new TypeReference<List<ScrapeRequest>>() {}
            );

            // Convert to CSV
            StringWriter stringWriter = new StringWriter();
            CSVWriter csvWriter = new CSVWriter(stringWriter);

            // Write header
            csvWriter.writeNext(new String[]{
                "name", "glassdoor", "clutch", "goodfirms", "linkedin", "website"
            });

            // Write data
            for (ScrapeRequest request : requests) {
                csvWriter.writeNext(new String[]{
                    request.getName(),
                    request.getGlassdoor(),
                    request.getClutch(),
                    request.getGoodfirms(),
                    request.getLinkedin(),
                    request.getWebsite()
                });
            }

            csvWriter.close();

            // Set up response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment", "companies.csv");

            return ResponseEntity
                .ok()
                .headers(headers)
                .body(stringWriter.toString().getBytes());

        } catch (Exception e) {
            log.error("Error converting JSON to CSV", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/upload")
    public ResponseEntity<?> uploadCsv(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty() || !file.getOriginalFilename().endsWith(".csv")) {
            return ResponseEntity.badRequest().body(Map.of("error", "Please upload a valid CSV file"));
        }

        List<String> errors = new ArrayList<>();
        List<ScrapeRequest> scrapeRequests = new ArrayList<>();
        String csvUploadId = UUID.randomUUID().toString();
        String csvFileName = file.getOriginalFilename();

        // Parse CSV and create scrape requests
        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream()))) {
            String[] header = reader.readNext(); // Read header
            if (header == null || !isValidHeader(header)) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid CSV format. Required columns: name, glassdoor, clutch, goodfirms, linkedin, website"));
            }

            Map<String, Integer> headerMap = createHeaderMap(header);
            String[] line;
            while ((line = reader.readNext()) != null) {
                try {
                    ScrapeRequest request = createScrapeRequest(line, headerMap);
                    request.setCsvFileName(csvFileName);
                    request.setCsvUploadId(csvUploadId);
                    scrapeRequests.add(request);
                } catch (Exception e) {
                    errors.add("Error processing line: " + String.join(",", line) + " - " + e.getMessage());
                }
            }
        } catch (IOException | CsvValidationException e) {
            log.error("Error processing CSV file", e);
            return ResponseEntity.internalServerError().body(Map.of("error", "Error processing CSV file: " + e.getMessage()));
        }

        // Create ScrapeRequestBatch and save as JSON file to input folder
        try {
            ScrapeRequestBatch batch = new ScrapeRequestBatch(scrapeRequests);
            batch.setBatchId(csvUploadId);
            batch.setDescription("CSV upload: " + csvFileName);
            batch.setCreatedAt(LocalDateTime.now());

            // Ensure input directory exists
            Path inputDir = Paths.get(fileWatcherProperties.getInputFolder());
            if (!Files.exists(inputDir)) {
                Files.createDirectories(inputDir);
            }

            // Save as JSON file with timestamp to avoid conflicts
            String jsonFileName = csvUploadId + "_" + csvFileName.replace(".csv", "") + ".json";
            Path jsonFile = inputDir.resolve(jsonFileName);
            objectMapper.writeValue(jsonFile.toFile(), batch);

            log.info("Saved CSV upload as JSON file: {} with {} requests", jsonFileName, scrapeRequests.size());

            Map<String, Object> response = new HashMap<>();
            response.put("successCount", scrapeRequests.size());
            response.put("csvUploadId", csvUploadId);
            response.put("csvFileName", csvFileName);
            response.put("jsonFileName", jsonFileName);
            response.put("message", "File uploaded successfully and queued for processing");
            if (!errors.isEmpty()) {
                response.put("errors", errors);
            }
            return ResponseEntity.ok(response);

        } catch (IOException e) {
            log.error("Error saving JSON file to input folder", e);
            return ResponseEntity.internalServerError().body(Map.of("error", "Error saving file for processing: " + e.getMessage()));
        }
    }

    @GetMapping("/csv-uploads")
    public ResponseEntity<List<Map<String, Object>>> getCsvUploads() {
        List<Map<String, Object>> uploads = scraperService.getCsvUploads();
        return ResponseEntity.ok(uploads);
    }

    @GetMapping("/download/{csvUploadId}")
    public ResponseEntity<byte[]> downloadScrapedCompanies(@PathVariable String csvUploadId) {
        try {
            List<ScrapeJob> scrapes = scraperService.getScrapesByCsvUploadId(csvUploadId);
            if (scrapes.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            StringWriter stringWriter = new StringWriter();
            CSVWriter csvWriter = new CSVWriter(stringWriter);

            // Write header
            csvWriter.writeNext(new String[]{
                // Basic Info
                "Company Name",
                "Last Scraped",
                "Completeness Score (%)",
                "Scraping Status",
                // Clutch Data
                "Clutch Status",
                "Clutch Rating",
                "Clutch Reviews Count",
                "Clutch Location",
                "Clutch Employees",
                "Clutch Hourly Rate",
                "Clutch Services",
                "Clutch Industries",
                "Clutch Company Overview",
                // Goodfirms Data
                "Goodfirms Status",
                "Goodfirms Rating",
                "Goodfirms Reviews Count",
                "Goodfirms Location",
                "Goodfirms Employees",
                "Goodfirms Services",
                "Goodfirms Client Likes",
                "Goodfirms Client Dislikes",
                // Glassdoor Data
                "Glassdoor Status",
                "Glassdoor Rating",
                "Glassdoor Reviews Count",
                "Glassdoor Employee Satisfaction",
                "Glassdoor Category Ratings",
                "Glassdoor Ratings Distribution",
                "Glassdoor Pros",
                "Glassdoor Cons",
                // LinkedIn Data
                "LinkedIn Status",
                "LinkedIn Followers",
                "LinkedIn Employees Count",
                "LinkedIn Industry",
                // Website Data
                "Website Status",
                "Website Founded Year",
                "Website HQ Location",
                "Website Employee Size",
                "Website Description",
                "Website Focus Statement",
                "Website Services",
                "Website Industries"
            });

            // Write data
            for (ScrapeJob scrape : scrapes) {
                // Get company data from output files
                Company company = getCompanyDataForScrapeJob(scrape);
                List<String> row = new ArrayList<>();

                // Basic Info
                row.add(scrape.getName());
                row.add(scrape.getUpdatedAt() != null ? scrape.getUpdatedAt().toString() : "");

                // Calculate completeness score and overall status
                double completenessScore = calculateCompletenessScore(company);
                String overallStatus = determineOverallScrapingStatus(company);
                row.add(String.format("%.1f", completenessScore));
                row.add(overallStatus);

                // Clutch Data
                row.add(company != null && company.getClutchStatus() != null ?
                    (company.getClutchStatus() ? "Success" : "Failed") : "Not Attempted");
                row.add(company != null && company.getClutchRating() != null ?
                    company.getClutchRating().toString() : "");
                row.add(company != null && company.getClutchReviewsCount() != null ?
                    company.getClutchReviewsCount().toString() : "");
                row.add(company != null ? safeString(company.getClutchLocation()) : "");
                row.add(company != null ? safeString(company.getClutchEmployees()) : "");
                row.add(company != null ? safeString(company.getClutchHourlyRate()) : "");
                row.add(company != null ? safeString(company.getClutchAllServices()) : "");
                row.add(company != null ? safeString(company.getClutchAllIndustries()) : "");
                row.add(company != null ? safeString(company.getClutchCompanyOverview()) : "");

                // Goodfirms Data
                row.add(company != null && company.getGoodfirmsStatus() != null ?
                    (company.getGoodfirmsStatus() ? "Success" : "Failed") : "Not Attempted");
                row.add(company != null && company.getGoodfirmsRating() != null ?
                    company.getGoodfirmsRating().toString() : "");
                row.add(company != null && company.getGoodfirmsReviewsCount() != null ?
                    company.getGoodfirmsReviewsCount().toString() : "");
                row.add(company != null ? safeString(company.getGoodfirmsLocation()) : "");
                row.add(company != null ? safeString(company.getGoodfirmsEmployees()) : "");
                row.add(company != null ? safeString(company.getGoodfirmsServices()) : "");
                row.add(company != null ? safeString(company.getGoodfirmsClientLikes()) : "");
                row.add(company != null ? safeString(company.getGoodfirmsClientDislikes()) : "");

                // Glassdoor Data
                row.add(company != null && company.getGlassdoorStatus() != null ?
                    (company.getGlassdoorStatus() ? "Success" : "Failed") : "Not Attempted");
                row.add(company != null && company.getGlassdoorRating() != null ?
                    company.getGlassdoorRating().toString() : "");
                row.add(company != null && company.getGlassdoorReviewsCount() != null ?
                    company.getGlassdoorReviewsCount().toString() : "");
                row.add(company != null && company.getGlassdoorEmployeeSatisfaction() != null ?
                    company.getGlassdoorEmployeeSatisfaction().toString() : "");
                row.add(company != null ? safeString(company.getGlassdoorCategoryRatings()) : "");
                row.add(company != null ? safeString(company.getGlassdoorRatingsDistribution()) : "");
                row.add(company != null ? safeString(company.getGlassdoorPros()) : "");
                row.add(company != null ? safeString(company.getGlassdoorCons()) : "");

                // LinkedIn Data
                row.add(company != null && company.getLinkedinStatus() != null ?
                    (company.getLinkedinStatus() ? "Success" : "Failed") : "Not Attempted");
                row.add(company != null && company.getLinkedinFollowers() != null ?
                    company.getLinkedinFollowers().toString() : "");
                row.add(company != null && company.getLinkedinEmployeesCount() != null ?
                    company.getLinkedinEmployeesCount().toString() : "");
                row.add(company != null ? safeString(company.getLinkedinIndustry()) : "");

                // Website Data
                row.add(company != null && company.getWebsiteStatus() != null ?
                    (company.getWebsiteStatus() ? "Success" : "Failed") : "Not Attempted");
                row.add(company != null ? safeString(company.getLinkedinFoundedYear()) : "");
                row.add(company != null ? safeString(company.getLinkedinLocation()) : "");
                row.add(company != null ? safeString(company.getLinkedinEmployeesCount() != null ?
                    company.getLinkedinEmployeesCount().toString() : "") : "");
                row.add(company != null ? safeString(company.getWebsiteDescription()) : "");
                row.add(company != null ? safeString(company.getWebsiteAboutUs()) : "");
                row.add(company != null ? safeString(company.getWebsiteServices()) : "");
                row.add(company != null ? safeString(company.getWebsiteIndustries()) : "");

                csvWriter.writeNext(row.toArray(new String[0]));
            }

            csvWriter.close();

            // Set up response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            String fileName = scrapes.get(0).getCsvFileName().replace(".csv", "_scraped.csv");
            headers.setContentDispositionFormData("attachment", fileName);

            return ResponseEntity
                .ok()
                .headers(headers)
                .body(stringWriter.toString().getBytes());

        } catch (Exception e) {
            log.error("Error generating CSV for upload ID: " + csvUploadId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    private boolean isValidHeader(String[] header) {
        List<String> requiredColumns = List.of("name", "glassdoor", "clutch", "goodfirms", "linkedin", "website");
        List<String> headerList = List.of(header);
        return headerList.containsAll(requiredColumns);
    }

    private Map<String, Integer> createHeaderMap(String[] header) {
        Map<String, Integer> headerMap = new HashMap<>();
        for (int i = 0; i < header.length; i++) {
            headerMap.put(header[i].toLowerCase().trim(), i);
        }
        return headerMap;
    }

    private ScrapeRequest createScrapeRequest(String[] line, Map<String, Integer> headerMap) {
        ScrapeRequest request = new ScrapeRequest();
        request.setName(getValue(line, headerMap, "name"));
        request.setGlassdoor(getValue(line, headerMap, "glassdoor"));
        request.setClutch(getValue(line, headerMap, "clutch"));
        request.setGoodfirms(getValue(line, headerMap, "goodfirms"));
        request.setLinkedin(getValue(line, headerMap, "linkedin"));
        request.setWebsite(getValue(line, headerMap, "website"));
        return request;
    }

    private String getValue(String[] line, Map<String, Integer> headerMap, String column) {
        Integer index = headerMap.get(column);
        if (index != null && index < line.length) {
            String value = line[index].trim();
            return value.isEmpty() ? null : value;
        }
        return null;
    }
    
    @Override
    @GetMapping("/queue")
    public ResponseEntity<List<ScrapeJob>> getAllQueuedScrapes() {
        log.info("Fetching all queued scrape requests");
        List<ScrapeJob> queuedScrapes = scraperService.getAllQueuedScrapes();
        return ResponseEntity.ok(queuedScrapes);
    }

    @Override
    @GetMapping("/scheduled")
    public ResponseEntity<List<ScrapeJob>> getAllScheduledScrapes() {
        log.info("Fetching all scheduled scrape requests");
        List<ScrapeJob> scheduledScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.SCHEDULED);
        return ResponseEntity.ok(scheduledScrapes);
    }

    @Override
    @GetMapping("/processing")
    public ResponseEntity<List<ScrapeJob>> getAllProcessingScrapes() {
        log.info("Fetching all processing scrape requests");
        List<ScrapeJob> processingScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.IN_PROGRESS);
        return ResponseEntity.ok(processingScrapes);
    }
    
    @Override
    @GetMapping("/completed")
    public ResponseEntity<List<ScrapeJob>> getAllCompletedScrapes() {
        log.info("Fetching all completed scrape requests");
        List<ScrapeJob> completedScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.SUCCESS);
        return ResponseEntity.ok(completedScrapes);
    }

    @Override
    @GetMapping("/failed")
    public ResponseEntity<List<ScrapeJob>> getAllFailedScrapes() {
        log.info("Fetching all failed scrape requests");
        List<ScrapeJob> failedScrapes = scraperService.getAllScrapesByStatus(ScrapeStatus.FAILED);
        return ResponseEntity.ok(failedScrapes);
    }
    
    /**
     * Debug endpoint to get system status overview
     */
    @GetMapping("/debug/status")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        log.info("Fetching system status overview");
        
        Map<String, Object> status = new HashMap<>();
        
        // Scrape entity counts
        status.put("queue", scraperService.getAllScrapesByStatus(ScrapeStatus.QUEUE).size());
        status.put("scheduled", scraperService.getAllScrapesByStatus(ScrapeStatus.SCHEDULED).size());
        status.put("processing", scraperService.getAllScrapesByStatus(ScrapeStatus.IN_PROGRESS).size());
        status.put("completed", scraperService.getAllScrapesByStatus(ScrapeStatus.SUCCESS).size());
        status.put("failed", scraperService.getAllScrapesByStatus(ScrapeStatus.FAILED).size());
        
        // Company statistics - simplified for file-based approach
        List<Company> allCompanies = scraperService.getAllCompanies();
        status.put("totalCompanies", allCompanies.size());

        // Calculate actual company statistics
        Map<String, Object> companyStats = calculateCompanyStatistics(allCompanies);

        status.put("companyStats", companyStats);
        
        // Add detailed info about queue items
        List<ScrapeJob> queueItems = scraperService.getAllScrapesByStatus(ScrapeStatus.QUEUE);
        List<Map<String, Object>> queueDetails = queueItems.stream().map(item -> {
            Map<String, Object> detail = new HashMap<>();
            detail.put("id", item.getId());
            detail.put("name", item.getName());
            detail.put("nextScheduledTime", item.getNextScheduledTime());
            detail.put("retryCount", item.getRetryCount());
            detail.put("csvUploadId", item.getCsvUploadId());
            return detail;
        }).toList();
        status.put("queueDetails", queueDetails);

        // Add detailed info about scheduled items
        List<ScrapeJob> scheduledItems = scraperService.getAllScrapesByStatus(ScrapeStatus.SCHEDULED);
        List<Map<String, Object>> scheduledDetails = scheduledItems.stream().map(item -> {
            Map<String, Object> detail = new HashMap<>();
            detail.put("id", item.getId());
            detail.put("name", item.getName());
            detail.put("scheduledTime", item.getNextScheduledTime());
            detail.put("minutesUntilReady", item.getNextScheduledTime() != null ?
                java.time.Duration.between(java.time.LocalDateTime.now(), item.getNextScheduledTime()).toMinutes() : 0);
            detail.put("csvUploadId", item.getCsvUploadId());
            return detail;
        }).toList();
        status.put("scheduledDetails", scheduledDetails);
        
        // Recent company activity - simplified for file-based approach
        List<Company> recentlyScraped = allCompanies.stream()
                .filter(c -> c.getUpdatedAt() != null)
                .sorted((c1, c2) -> c2.getUpdatedAt().compareTo(c1.getUpdatedAt()))
                .limit(5)
                .toList();

        List<Map<String, Object>> recentActivity = recentlyScraped.stream().map(company -> {
            Map<String, Object> activity = new HashMap<>();
            activity.put("companyId", company.getId());
            activity.put("name", company.getName());
            activity.put("lastScraped", company.getUpdatedAt());
            activity.put("completenessScore", calculateCompletenessScore(company));
            return activity;
        }).toList();
        status.put("recentActivity", recentActivity);
        
        status.put("currentTime", java.time.LocalDateTime.now());
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * Debug endpoint to create a test scrape request quickly
     */
    @PostMapping("/debug/test-request")
    public ResponseEntity<Map<String, String>> createTestRequest() {
        log.info("Creating test scrape request");
        
        ScrapeRequest testRequest = new ScrapeRequest();
        testRequest.setName("Test Company " + System.currentTimeMillis());
        testRequest.setGlassdoor("https://glassdoor.com/test");
        testRequest.setClutch("https://clutch.co/test");
        testRequest.setGoodfirms("https://goodfirms.co/test");
        testRequest.setWebsite("https://test.com");
        
        scraperService.saveScrapeRequest(testRequest);
        
        return ResponseEntity.ok(Map.of("message", "Test request created: " + testRequest.getName()));
    }
    
    /**
     * Get all companies with their scraped data
     */
    @GetMapping("/companies")
    public ResponseEntity<List<Company>> getAllCompanies() {
        log.info("Fetching all companies");
        List<Company> companies = scraperService.getAllCompanies();
        return ResponseEntity.ok(companies);
    }

    /**
     * Get company by ID
     */
    @GetMapping("/companies/{id}")
    public ResponseEntity<Company> getCompanyById(@PathVariable String id) {
        log.info("Fetching company with ID: {}", id);
        return scraperService.getCompanyById(id)
                .map(company -> ResponseEntity.ok(company))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * Get companies by completeness score
     */
    @GetMapping("/companies/by-completeness")
    public ResponseEntity<List<Company>> getCompaniesByCompleteness(
            @RequestParam(defaultValue = "50.0") Double minScore) {
        log.info("Fetching companies with completeness score >= {}", minScore);
        List<Company> companies = scraperService.getCompaniesByCompleteness(minScore);
        return ResponseEntity.ok(companies);
    }

    /**
     * Helper method to get company data for a scrape job
     */
    private Company getCompanyDataForScrapeJob(ScrapeJob scrapeJob) {
        if (scrapeJob.getCompanyId() != null) {
            return scraperService.getCompanyById(scrapeJob.getCompanyId()).orElse(null);
        }

        // If no company ID, try to find company by name
        List<Company> allCompanies = scraperService.getAllCompanies();
        return allCompanies.stream()
                .filter(company -> company.getName() != null &&
                        company.getName().equalsIgnoreCase(scrapeJob.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * Helper method to calculate completeness score for a company
     * Based on successful scraping of 5 platforms: Clutch, Goodfirms, Glassdoor, LinkedIn, Website
     */
    private double calculateCompletenessScore(Company company) {
        if (company == null) {
            return 0.0;
        }

        int successfulScrapes = 0;
        int totalPlatforms = 5;

        // Check each platform status
        if (company.getClutchStatus() != null && company.getClutchStatus()) {
            successfulScrapes++;
        }
        if (company.getGoodfirmsStatus() != null && company.getGoodfirmsStatus()) {
            successfulScrapes++;
        }
        if (company.getGlassdoorStatus() != null && company.getGlassdoorStatus()) {
            successfulScrapes++;
        }
        if (company.getLinkedinStatus() != null && company.getLinkedinStatus()) {
            successfulScrapes++;
        }
        if (company.getWebsiteStatus() != null && company.getWebsiteStatus()) {
            successfulScrapes++;
        }

        return (double) successfulScrapes / totalPlatforms * 100.0;
    }

    /**
     * Helper method to determine overall scraping status
     */
    private String determineOverallScrapingStatus(Company company) {
        if (company == null) {
            return "Not Started";
        }

        int successfulScrapes = 0;
        int attemptedScrapes = 0;

        // Count successful and attempted scrapes
        if (company.getClutchStatus() != null) {
            attemptedScrapes++;
            if (company.getClutchStatus()) successfulScrapes++;
        }
        if (company.getGoodfirmsStatus() != null) {
            attemptedScrapes++;
            if (company.getGoodfirmsStatus()) successfulScrapes++;
        }
        if (company.getGlassdoorStatus() != null) {
            attemptedScrapes++;
            if (company.getGlassdoorStatus()) successfulScrapes++;
        }
        if (company.getLinkedinStatus() != null) {
            attemptedScrapes++;
            if (company.getLinkedinStatus()) successfulScrapes++;
        }
        if (company.getWebsiteStatus() != null) {
            attemptedScrapes++;
            if (company.getWebsiteStatus()) successfulScrapes++;
        }

        if (attemptedScrapes == 0) {
            return "Not Started";
        } else if (successfulScrapes == attemptedScrapes) {
            return "Completed Successfully";
        } else if (successfulScrapes > 0) {
            return "Partially Completed";
        } else {
            return "Failed";
        }
    }

    /**
     * Helper method to safely convert string to non-null value
     */
    private String safeString(String value) {
        return value != null ? value : "";
    }

    /**
     * Helper method to calculate company statistics
     */
    private Map<String, Object> calculateCompanyStatistics(List<Company> companies) {
        Map<String, Object> stats = new HashMap<>();

        int clutchScraped = 0;
        int goodfirmsScraped = 0;
        int glassdoorScraped = 0;
        int linkedinScraped = 0;
        int websiteScraped = 0;
        double totalCompleteness = 0.0;

        for (Company company : companies) {
            if (company.getClutchStatus() != null && company.getClutchStatus()) {
                clutchScraped++;
            }
            if (company.getGoodfirmsStatus() != null && company.getGoodfirmsStatus()) {
                goodfirmsScraped++;
            }
            if (company.getGlassdoorStatus() != null && company.getGlassdoorStatus()) {
                glassdoorScraped++;
            }
            if (company.getLinkedinStatus() != null && company.getLinkedinStatus()) {
                linkedinScraped++;
            }
            if (company.getWebsiteStatus() != null && company.getWebsiteStatus()) {
                websiteScraped++;
            }

            totalCompleteness += calculateCompletenessScore(company);
        }

        stats.put("clutchScraped", clutchScraped);
        stats.put("goodfirmsScraped", goodfirmsScraped);
        stats.put("glassdoorScraped", glassdoorScraped);
        stats.put("linkedinScraped", linkedinScraped);
        stats.put("websiteScraped", websiteScraped);
        stats.put("averageCompleteness", companies.isEmpty() ? 0.0 : totalCompleteness / companies.size());

        return stats;
    }
}
