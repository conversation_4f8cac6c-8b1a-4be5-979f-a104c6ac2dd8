package com.enosisbd.app.controller;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import com.enosisbd.app.controller.request.ScrapeRequest;
import com.enosisbd.app.model.ScrapeJob;

public interface ScraperApi {
    ResponseEntity<?> scrape(ScrapeRequest scrapeRequest);

    /**
     * Convert the enosis.json file to CSV format
     * @return ResponseEntity containing the CSV file as bytes
     */
    ResponseEntity<byte[]> convertJsonToCsv();

    /**
     * Upload a CSV file containing multiple scrape requests
     * @param file The CSV file containing company data
     * @return ResponseEntity containing upload status and any errors
     */
    ResponseEntity<?> uploadCsv(MultipartFile file);

    /**
     * Get all scrape jobs with QUEUE status
     * @return ResponseEntity containing list of queued scrape jobs
     */
    ResponseEntity<List<ScrapeJob>> getAllQueuedScrapes();

    /**
     * Get all scrape jobs with SCHEDULED status
     * @return ResponseEntity containing list of scheduled scrape jobs
     */
    ResponseEntity<List<ScrapeJob>> getAllScheduledScrapes();

    /**
     * Get all scrape jobs currently IN_PROGRESS
     * @return ResponseEntity containing list of processing scrape jobs
     */
    ResponseEntity<List<ScrapeJob>> getAllProcessingScrapes();

    /**
     * Get all scrape jobs with SUCCESS status
     * @return ResponseEntity containing list of completed scrape jobs
     */
    ResponseEntity<List<ScrapeJob>> getAllCompletedScrapes();

    /**
     * Get all scrape jobs with FAILED status
     * @return ResponseEntity containing list of failed scrape jobs
     */
    ResponseEntity<List<ScrapeJob>> getAllFailedScrapes();
}
