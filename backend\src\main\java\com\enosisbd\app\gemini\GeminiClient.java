package com.enosisbd.app.gemini;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "geminiClient", url = "https://generativelanguage.googleapis.com")
public interface GeminiClient {
    @PostMapping(
            value = "/v1beta/models/{modelName}:generateContent",
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    GeminiResponse generateContent(
            @PathVariable("modelName") String modelName,
            @RequestParam("key") String apiKey,
            @RequestBody GeminiRequest request
    );
}