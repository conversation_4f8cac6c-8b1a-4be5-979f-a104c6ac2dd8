package com.enosisbd.app.service.impl.goodfirms;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.enosisbd.app.model.CompanyDataModel;
import com.enosisbd.app.service.GoodfirmsScraperService;
import com.enosisbd.app.service.crawler.GenericCrawlerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoodfirmsScraperServiceImpl implements GoodfirmsScraperService {

    // Base URL for Goodfirms
    private static final String BASE_URL = "https://www.goodfirms.co";

    @Value("${scraping.timeout-ms:120000}")
    private int TIMEOUT_MS;

    @Value("${scraping.goodfirms.login-required:false}")
    private boolean loginRequired;

    @Value("${scraping.anti-detection.enabled:true}")
    private boolean antiDetectionEnabled;

    @Value("${scraping.anti-detection.random-delays:true}")
    private boolean useRandomDelays;

    @Value("${scraping.anti-detection.min-delay-ms:500}")
    private int minDelayMs;

    @Value("${scraping.anti-detection.max-delay-ms:3000}")
    private int maxDelayMs;

    // Additional timeout configurations
    @Value("${scraping.goodfirms.basic-data-timeout-ms:30000}")
    private int basicDataTimeoutMs;
    
    @Value("${scraping.goodfirms.logo-timeout-ms:15000}")
    private int logoTimeoutMs;
    
    @Value("${scraping.goodfirms.feedback-timeout-ms:45000}")
    private int feedbackTimeoutMs;
    
    @Value("${scraping.goodfirms.page-load-timeout-ms:20000}")
    private int pageLoadTimeoutMs;
    
    @Value("${scraping.wait-times.review-section-wait-ms:10000}")
    private int reviewSectionWaitMs;
    
    @Value("${scraping.retry.max-attempts:3}")
    private int maxRetryAttempts;
    
    @Value("${scraping.retry.backoff-multiplier:2000}")
    private long backoffMultiplier;

    private final GenericCrawlerService genericCrawlerService;

    @Override
    public CompanyDataModel scrapeFromGoodFirms(String url) {
        log.info("Starting to scrape Goodfirms data for company: {}", url);
        CompanyDataModel companyDataModel = CompanyDataModel.success("goodfirms");
        long startTime = System.currentTimeMillis();
        
        int maxRetries = maxRetryAttempts;
        int retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                log.info("Scraping attempt {} of {} for URL: {}", retryCount + 1, maxRetries, url);
                
                // Step 1: Build profile URL and create scraping options
                Map<String, Object> scrapingOptions = createEnhancedScrapingOptions();

                // Step 2: Scrape basic company information
                Map<String, String> basicData = scrapeBasicCompanyData(url, scrapingOptions);

                // Step 3: Scrape company logo
                Map<String, String> logoData = scrapeCompanyLogo(url);

                // Step 4: Scrape client feedback (likes and dislikes)
                Map<String, String> feedbackData = scrapeClientFeedback(url, scrapingOptions);

                // Step 5: Combine all scraped data
                Map<String, String> allScrapedData = combineScrapedData(basicData, logoData, feedbackData);

                // Step 6: Populate the Company object
                populateCompanyObject(companyDataModel, allScrapedData, url);

                logScrapingSuccess(url, startTime);
                return companyDataModel;

            } catch (Exception e) {
                retryCount++;
                log.error("Error scraping GoodFirms data for company {} (attempt {} of {}): {}", 
                         url, retryCount, maxRetries, e.getMessage());
                
                if (retryCount < maxRetries) {
                    try {
                        // Wait before retrying (exponential backoff)
                        long waitTime = backoffMultiplier * retryCount;
                        log.info("Waiting {} ms before retry...", waitTime);
                        Thread.sleep(waitTime);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("Interrupted while waiting for retry");
                        break;
                    }
                } else {
                    log.error("All retry attempts failed for GoodFirms scraping: {}", url);
                    return CompanyDataModel.failed("goodfirms", 
                        String.format("Failed after %d attempts. Last error: %s", maxRetries, e.getMessage()));
                }
            }
        }
        
        return CompanyDataModel.failed("goodfirms", "Scraping failed after all retry attempts");
    }

    /**
     * Creates enhanced scraping options with JavaScript support
     */
    private Map<String, Object> createEnhancedScrapingOptions() {
        Map<String, Object> options = new HashMap<>();

        // Basic options
        options.put("headless", true);
        options.put("useRandomDelays", useRandomDelays);
        options.put("timeout", TIMEOUT_MS);
        options.put("antiDetectionEnabled", antiDetectionEnabled);
        options.put("minDelayMs", minDelayMs);
        options.put("maxDelayMs", maxDelayMs);

        // Enhanced options for JavaScript-rendered content
        options.put("waitForSelector", "#review_analytics");
        options.put("waitTime", 5000);
        options.put("scrollToElement", true);
        options.put("enableJavaScript", true);

        log.info("Created enhanced scraping options: {}", options);
        return options;
    }

    /**
     * Creates basic scraping options for simple content
     */
    private Map<String, Object> createBasicScrapingOptions() {
        Map<String, Object> options = new HashMap<>();
        options.put("headless", true);
        options.put("useRandomDelays", useRandomDelays);
        options.put("timeout", TIMEOUT_MS);
        options.put("antiDetectionEnabled", antiDetectionEnabled);
        return options;
    }

    /**
     * Scrapes basic company information using CSS selectors
     */
    private Map<String, String> scrapeBasicCompanyData(String profileUrl, Map<String, Object> options) {
        log.info("Scraping basic company data from: {}", profileUrl);

        // Override timeout for basic data scraping
        Map<String, Object> basicDataOptions = new HashMap<>(options);
        basicDataOptions.put("timeout", basicDataTimeoutMs);
        basicDataOptions.put("waitTime", pageLoadTimeoutMs);

        Map<String, String> selectors = createBasicCompanySelectors();
        Map<String, String> scrapedData = genericCrawlerService.scrapeWithSelectors(profileUrl, selectors, basicDataOptions);

        logScrapingResults("Basic company data", scrapedData);
        return scrapedData;
    }

    /**
     * Scrapes company logo URL
     */
    private Map<String, String> scrapeCompanyLogo(String profileUrl) {
        log.info("Scraping company logo from: {}", profileUrl);

        Map<String, String> logoSelectors = new HashMap<>();
        logoSelectors.put("companyLogo", "#header > div > div > div.profile-header-details-wrap.gap-20 > div.profile-header-info.gap-20 > div.profile-header-logo.d-flex.flex-center > img");

        Map<String, Object> imageOptions = new HashMap<>();
        imageOptions.put("headless", true);
        imageOptions.put("extractAttributes", true);
        imageOptions.put("attributeName", "src");
        imageOptions.put("timeout", logoTimeoutMs);

        Map<String, String> logoData = genericCrawlerService.scrapeWithSelectors(profileUrl, logoSelectors, imageOptions);
        logScrapingResults("Company logo", logoData);
        return logoData;
    }

    /**
     * Scrapes client feedback (likes and dislikes) using XPath selectors
     */
    private Map<String, String> scrapeClientFeedback(String profileUrl, Map<String, Object> options) {
        log.info("Scraping client feedback from: {}", profileUrl);

        // Override timeout for feedback scraping (this takes longer)
        Map<String, Object> feedbackOptions = new HashMap<>(options);
        feedbackOptions.put("timeout", feedbackTimeoutMs);
        feedbackOptions.put("waitTime", reviewSectionWaitMs);
        feedbackOptions.put("waitForSelector", "#review_analytics");

        Map<String, String> xpathSelectors = createClientFeedbackSelectors();
        Map<String, String> feedbackData = genericCrawlerService.scrapeWithXPath(profileUrl, xpathSelectors, feedbackOptions);

        logScrapingResults("Client feedback", feedbackData);
        return feedbackData;
    }

    /**
     * Creates CSS selectors for basic company information
     */
    private Map<String, String> createBasicCompanySelectors() {
        Map<String, String> selectors = new HashMap<>();

        // Use the specific selectors that work for GoodFirms
        selectors.put("companyName", "#header > div > div > div.profile-header-details-wrap.gap-20 > div.profile-header-info.gap-20 > div.profile-header-data.gap-10 > h1");
        selectors.put("companyRating", "span[itemprop='ratingValue']");
        selectors.put("companyLocation", "#overview > div > div > div.profile-locations-wrap.flex-column.align-items-start.gap-15.box-padding.single-location > div:nth-child(1) > div.profile-location-address");
        selectors.put("companyDescription", "#overview > div > div > div.profile-details-wrap.box-padding > div > div.profile-summary-text.flex-column.align-items-start.gap-15");
        selectors.put("reviewsCount", "#header > div > div > div.profile-header-details-wrap.gap-20 > div.profile-header-info.gap-20 > div.profile-header-data.gap-10 > div > div > span.review-count > span");
        selectors.put("companyOverview", "#header > div > div > div.profile-header-details-wrap.gap-20 > div.profile-header-actions > a");

        return selectors;
    }

    /**
     * Creates XPath selectors for client feedback extraction
     */
    private Map<String, String> createClientFeedbackSelectors() {
        Map<String, String> xpathSelectors = new HashMap<>();

        // Primary strategy: Full XPath (working selectors)
        xpathSelectors.put("clientLikes", "/html/body/main/div[2]/section[3]/div/div/div[1]/div[3]/div[1]/ul");
        xpathSelectors.put("clientDislikes", "/html/body/main/div[2]/section[3]/div/div/div[1]/div[3]/div[2]/ul");
        xpathSelectors.put("verificationBadge", "/html/body/main/section/div/div/div[1]/div[1]/div[2]/div/span[2]");
        // Fallback strategies (commented out but available for debugging)
        // xpathSelectors.put("clientLikesClass", "//ul[contains(@class, 'strength-weakness-wrapper')][1]");
        // xpathSelectors.put("clientDislikesClass", "//ul[contains(@class, 'strength-weakness-wrapper')][2]");

        return xpathSelectors;
    }

    /**
     * Logs scraping results in a consistent format
     */
    private void logScrapingResults(String dataType, Map<String, String> scrapedData) {
        log.info("{} scraping results:", dataType);
        for (Map.Entry<String, String> entry : scrapedData.entrySet()) {
            String value = entry.getValue();
            if (value != null && value.length() > 50) {
                log.info("  {}: {} characters - {}", entry.getKey(), value.length(), value.substring(0, 50) + "...");
            } else {
                log.info("  {}: {}", entry.getKey(), value);
            }
        }
    }

    /**
     * Combines multiple scraped data maps into one
     */
    private Map<String, String> combineScrapedData(Map<String, String> basicData,
                                                   Map<String, String> logoData,
                                                   Map<String, String> feedbackData) {
        Map<String, String> combinedData = new HashMap<>();

        if (basicData != null) {
            combinedData.putAll(basicData);
        }
        if (logoData != null) {
            combinedData.putAll(logoData);
        }
        if (feedbackData != null) {
            combinedData.putAll(feedbackData);
        }

        log.info("Combined scraped data: {} total fields", combinedData.size());
        return combinedData;
    }

    /**
     * Logs successful scraping completion
     */
    private void logScrapingSuccess(String companySlug, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        log.info("✅ Successfully scraped GoodFirms data for {} in {} ms", companySlug, duration);
    }


    /**
     * Populates the Company object with scraped data following Clutch pattern
     */
    private void populateCompanyObject(CompanyDataModel companyDataModel, Map<String, String> scrapedData, String profileUrl) {
        // Basic Information
        if (companyDataModel.getGoodfirmProviderName() == null && scrapedData.containsKey("companyName")) {
            companyDataModel.setGoodfirmProviderName(scrapedData.get("companyName"));
        }

        if (companyDataModel.getName() == null && scrapedData.containsKey("companyName")) {
            companyDataModel.setName(scrapedData.get("companyName"));
        }

        if (companyDataModel.getCompanyLogoURL() == null && scrapedData.containsKey("companyLogo")) {
            companyDataModel.setCompanyLogoURL(scrapedData.get("companyLogo"));
        }

        if (companyDataModel.getGoodfirmRatings() == null && scrapedData.containsKey("companyRating")) {
            companyDataModel.setGoodfirmRatings(scrapedData.get("companyRating"));
            
            // Also try to parse as numeric rating for the goodfirmsRating field
            try {
                String ratingStr = scrapedData.get("companyRating");
                if (ratingStr != null && !ratingStr.trim().isEmpty()) {
                    // Extract numeric part from rating (e.g., "4.5/5" -> 4.5)
                    String numericPart = ratingStr.replaceAll("[^0-9.]", "");
                    if (!numericPart.isEmpty()) {
                        companyDataModel.setGoodfirmsRating(Double.parseDouble(numericPart));
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("Could not parse Goodfirms rating as number: {}", scrapedData.get("companyRating"));
            }
        }

        // Parse reviews count
        if (companyDataModel.getGoodfirmsReviewsCount() == null && scrapedData.containsKey("reviewsCount")) {
            try {
                String reviewsStr = scrapedData.get("reviewsCount");
                if (reviewsStr != null && !reviewsStr.trim().isEmpty()) {
                    // Extract numeric part from reviews count
                    String numericPart = reviewsStr.replaceAll("[^0-9]", "");
                    if (!numericPart.isEmpty()) {
                        companyDataModel.setGoodfirmsReviewsCount(Integer.parseInt(numericPart));
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("Could not parse Goodfirms reviews count as number: {}", scrapedData.get("reviewsCount"));
            }
        }

        // Location parsing
        if (companyDataModel.getCity() == null && scrapedData.containsKey("companyLocation")) {
            String locationText = scrapedData.get("companyLocation");
            if (locationText != null && !locationText.isEmpty()) {
                companyDataModel.setGoodfirmsLocation(locationText);
                
                // Try to parse "City, Country" format
                String[] parts = locationText.split(",");
                if (parts.length >= 1) {
                    companyDataModel.setCity(parts[0].trim());
                }
                if (parts.length >= 2) {
                    companyDataModel.setCountry(parts[1].trim());
                }
            }
        }

        // Verification badge
        if (companyDataModel.getGoodfirmVerificationBadge() == null && scrapedData.containsKey("verificationBadge")) {
            String badgeText = scrapedData.get("verificationBadge");
            boolean isVerified = badgeText != null && (badgeText.toLowerCase().contains("verified") || badgeText.toLowerCase().contains("badge"));
            companyDataModel.setGoodfirmVerificationBadge(isVerified);
        }

        // Company overview/description
        if (companyDataModel.getCompanyOverview() == null && scrapedData.containsKey("companyDescription")) {
            companyDataModel.setCompanyOverview(scrapedData.get("companyDescription"));
        }

        // Set GoodFirms specific fields
        if (companyDataModel.getGoodfirmProfileUrl() == null) {
            companyDataModel.setGoodfirmProfileUrl(profileUrl);
        }

        // Extract and set client likes and dislikes with fallback strategies
        if (companyDataModel.getClientLikes() == null) {
            log.info("Extracting client likes...");
            List<String> likes = extractClientFeedbackWithFallback(scrapedData,
                    "clientLikes", "clientLikesClass", "clientLikesAlt", "clientLikesFlexible", "clientLikesAltPath",
                    "allFeedbackLists", "strengthWeaknessElements", "client_likes", "client_likes_alt", "client_likes_original");
            if (!likes.isEmpty()) {
                companyDataModel.setClientLikes(likes);
                log.info("✅ Successfully set {} client likes:", likes.size());
                for (int i = 0; i < Math.min(likes.size(), 3); i++) {
                    log.info("  Like {}: {}", i + 1, likes.get(i));
                }
            } else {
                log.warn("❌ No client likes found");
            }
        } else {
            log.info("Client likes already set, skipping extraction");
        }

        if (companyDataModel.getClientDislikes() == null) {
            log.info("Extracting client dislikes...");
            List<String> dislikes = extractClientFeedbackWithFallback(scrapedData,
                    "clientDislikes", "clientDislikesClass", "clientDislikesAlt", "clientDislikesFlexible", "clientDislikesAltPath",
                    "allFeedbackLists", "strengthWeaknessElements", "client_dislikes", "client_dislikes_alt", "client_dislikes_original");
            if (!dislikes.isEmpty()) {
                companyDataModel.setClientDislikes(dislikes);
                log.info("✅ Successfully set {} client dislikes:", dislikes.size());
                for (int i = 0; i < Math.min(dislikes.size(), 3); i++) {
                    log.info("  Dislike {}: {}", i + 1, dislikes.get(i));
                }
            } else {
                log.warn("❌ No client dislikes found");
            }
        } else {
            log.info("Client dislikes already set, skipping extraction");
        }

        log.info("Company object population completed for Goodfirms data");
    }

    /**
     * Extracts client feedback with fallback strategies
     */
    private List<String> extractClientFeedbackWithFallback(Map<String, String> scrapedData, String... keys) {
        log.info("Attempting to extract client feedback with keys: {}", String.join(", ", keys));

        for (String key : keys) {
            log.info("Checking key: {}", key);
            if (scrapedData.containsKey(key)) {
                String data = scrapedData.get(key);
                log.info("Found data for key {}: {}", key, data != null ? (data.length() > 100 ? data.substring(0, 100) + "..." : data) : "null");

                if (data != null && !data.trim().isEmpty() && !data.startsWith("ERROR:")) {
                    List<String> items = extractListFromHtml(data);
                    log.info("Extracted {} items from key {}", items.size(), key);
                    if (!items.isEmpty()) {
                        log.info("Successfully extracted client feedback using key: {}", key);
                        for (int i = 0; i < items.size(); i++) {
                            log.info("  Item {}: {}", i + 1, items.get(i));
                        }
                        return items;
                    }
                } else {
                    log.warn("Data for key {} is empty or error: {}", key, data);
                }
            } else {
                log.warn("Key {} not found in scraped data", key);
            }
        }
        log.warn("No client feedback found with any of the provided keys: {}", String.join(", ", keys));
        return new ArrayList<>();
    }

    /**
     * Extracts list items from HTML content (for client likes/dislikes)
     * Each line becomes a separate item in the list
     */
    private List<String> extractListFromHtml(String htmlContent) {
        List<String> items = new ArrayList<>();
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return items;
        }

        log.info("Processing HTML content: {}", htmlContent.length() > 200 ? htmlContent.substring(0, 200) + "..." : htmlContent);

        // Step 1: Clean HTML content and preserve structure
        String cleaned = htmlContent;

        // Remove HTML tags but preserve line breaks from <li> elements
        cleaned = cleaned.replaceAll("<li[^>]*>", "\n").replaceAll("</li>", "\n");
        cleaned = cleaned.replaceAll("<ul[^>]*>", "").replaceAll("</ul>", "");
        cleaned = cleaned.replaceAll("<[^>]*>", ""); // Remove all other HTML tags
        cleaned = cleaned.trim();

        log.info("After HTML cleaning: {}", cleaned.length() > 200 ? cleaned.substring(0, 200) + "..." : cleaned);

        // Step 2: Split by line breaks and process each line
        String[] lines = cleaned.split("\\n");
        for (String line : lines) {
            String trimmedLine = line.trim();

            // Filter out empty lines and very short content
            if (!trimmedLine.isEmpty() && trimmedLine.length() > 5) {
                // Remove extra whitespace and normalize
                trimmedLine = trimmedLine.replaceAll("\\s+", " ");

                // Skip common non-content lines
                if (!isNonContentLine(trimmedLine)) {
                    items.add(trimmedLine);
                    log.info("Added item: {}", trimmedLine);
                }
            }
        }

        // Step 3: If no items found with line break splitting, try alternative methods
        if (items.isEmpty()) {
            log.info("No items found with line splitting, trying sentence patterns...");

            // Try splitting by sentence patterns (sentences ending with period followed by capital letter)
            String[] sentences = cleaned.split("(?<=\\.)\\s+(?=[A-Z])");
            for (String sentence : sentences) {
                String trimmedSentence = sentence.trim();
                if (!trimmedSentence.isEmpty() && trimmedSentence.length() > 10) {
                    if (!isNonContentLine(trimmedSentence)) {
                        items.add(trimmedSentence);
                        log.info("Added sentence: {}", trimmedSentence);
                    }
                }
            }
        }

        // Step 4: If still no items, try splitting by common delimiters
        if (items.isEmpty()) {
            log.info("No items found with sentence splitting, trying delimiter patterns...");

            // Try splitting by bullet points, numbers, or other common patterns
            String[] parts = cleaned.split("(?=\\d+\\.|•|\\*|-)");
            for (String part : parts) {
                String trimmedPart = part.trim();
                // Remove leading numbers, bullets, etc.
                trimmedPart = trimmedPart.replaceAll("^\\d+\\.\\s*", "");
                trimmedPart = trimmedPart.replaceAll("^[•\\*-]\\s*", "");
                trimmedPart = trimmedPart.trim();

                if (!trimmedPart.isEmpty() && trimmedPart.length() > 10) {
                    if (!isNonContentLine(trimmedPart)) {
                        items.add(trimmedPart);
                        log.info("Added part: {}", trimmedPart);
                    }
                }
            }
        }

        log.info("Final extracted items count: {}", items.size());
        return items;
    }

    /**
     * Checks if a line is likely non-content (headers, labels, etc.)
     */
    private boolean isNonContentLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return true;
        }

        String lowerLine = line.toLowerCase().trim();

        // Skip common non-content patterns
        return lowerLine.equals("client likes") ||
                lowerLine.equals("client dislikes") ||
                lowerLine.equals("strengths") ||
                lowerLine.equals("weaknesses") ||
                lowerLine.equals("pros") ||
                lowerLine.equals("cons") ||
                lowerLine.length() < 5 ||
                lowerLine.matches("^\\d+$") || // Just numbers
                lowerLine.matches("^[•\\*-]+$"); // Just bullets
    }


    /**
     * Debug method to test client feedback extraction
     * Call this method to debug what's happening with the scraping
     */
    public void debugClientFeedbackExtraction(String companySlug) {
        String profileUrl = BASE_URL + "/company/" + companySlug.toLowerCase().replace(" ", "-");
        log.info("=== DEBUGGING CLIENT FEEDBACK EXTRACTION ===");
        log.info("Profile URL: {}", profileUrl);

        // Set up options
        Map<String, Object> options = new HashMap<>();
        options.put("headless", false); // Run in non-headless mode to see what's happening
        options.put("useRandomDelays", false); // Disable delays for faster debugging
        options.put("timeout", TIMEOUT_MS);
        options.put("antiDetectionEnabled", true);

        // Add options to handle JavaScript-rendered content
        options.put("waitForSelector", "#review_analytics"); // Wait for review analytics section
        options.put("waitTime", 10000); // Wait 10 seconds for content to load (longer for debugging)
        options.put("scrollToElement", true); // Scroll to trigger lazy loading
        options.put("enableJavaScript", true); // Ensure JavaScript is enabled

        // Test all XPath selectors
        Map<String, String> xpathSelectors = new HashMap<>();
        xpathSelectors.put("clientLikes", "//ul[contains(@class, 'strength-weakness-wrapper')][1]");
        xpathSelectors.put("clientDislikes", "//ul[contains(@class, 'strength-weakness-wrapper')][2]");
        xpathSelectors.put("clientLikesAlt", "//*[@id='review_analytics']//ul[contains(@class, 'strength-weakness-wrapper')][1]");
        xpathSelectors.put("clientDislikesAlt", "//*[@id='review_analytics']//ul[contains(@class, 'strength-weakness-wrapper')][2]");
        xpathSelectors.put("clientLikesOriginal", "//*[@id=\"review_analytics\"]/div/div/div[1]/div[3]/div[1]/ul");
        xpathSelectors.put("clientDislikesOriginal", "//*[@id=\"review_analytics\"]/div/div/div[1]/div[3]/div[2]/ul");
        xpathSelectors.put("allUlElements", "//ul");
        xpathSelectors.put("reviewAnalyticsSection", "//*[@id='review_analytics']");
        xpathSelectors.put("strengthWeaknessElements", "//*[contains(@class, 'strength-weakness-wrapper')]");

        log.info("Testing {} XPath selectors...", xpathSelectors.size());

        try {
            Map<String, String> results = genericCrawlerService.scrapeWithXPath(profileUrl, xpathSelectors, options);

            log.info("=== SCRAPING RESULTS ===");
            for (Map.Entry<String, String> entry : results.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                if (value != null && !value.trim().isEmpty() && !value.startsWith("ERROR:")) {
                    log.info("✅ SUCCESS - {}: {} characters", key, value.length());
                    if (value.length() > 200) {
                        log.info("   Preview: {}", value.substring(0, 200) + "...");
                    } else {
                        log.info("   Full content: {}", value);
                    }

                    // Try to extract list items
                    List<String> items = extractListFromHtml(value);
                    log.info("   Extracted {} list items", items.size());
                    for (int i = 0; i < Math.min(items.size(), 3); i++) {
                        log.info("     Item {}: {}", i + 1, items.get(i));
                    }
                } else {
                    log.warn("❌ FAILED - {}: {}", key, value);
                }
            }

        } catch (Exception e) {
            log.error("Error during debugging: {}", e.getMessage(), e);
        }

        log.info("=== END DEBUGGING ===");
    }

    /**
     * Simple test method to check if the page loads and basic elements exist
     */
    public void testBasicPageAccess(String companySlug) {
        String profileUrl = BASE_URL + "/company/" + companySlug.toLowerCase().replace(" ", "-");
        log.info("=== TESTING BASIC PAGE ACCESS ===");
        log.info("Profile URL: {}", profileUrl);

        // Test 1: Check if page loads at all
        Map<String, Object> basicOptions = new HashMap<>();
        basicOptions.put("headless", false);
        basicOptions.put("timeout", TIMEOUT_MS);

        Map<String, String> basicSelectors = new HashMap<>();
        basicSelectors.put("pageTitle", "//title");
        basicSelectors.put("bodyContent", "//body");
        basicSelectors.put("mainContent", "//main");

        try {
            log.info("Step 1: Testing basic page access...");
            Map<String, String> basicResults = genericCrawlerService.scrapeWithXPath(profileUrl, basicSelectors, basicOptions);

            for (Map.Entry<String, String> entry : basicResults.entrySet()) {
                String value = entry.getValue();
                if (value != null && !value.trim().isEmpty()) {
                    log.info("✅ {} found: {} characters", entry.getKey(), value.length());
                } else {
                    log.warn("❌ {} not found", entry.getKey());
                }
            }

            // Test 2: Check if review analytics section exists
            log.info("Step 2: Testing review analytics section...");
            Map<String, String> reviewSelectors = new HashMap<>();
            reviewSelectors.put("reviewAnalytics", "//*[@id='review_analytics']");
            reviewSelectors.put("reviewAnalyticsAny", "//*[contains(@id, 'review')]");
            reviewSelectors.put("anySection", "//section");

            Map<String, String> reviewResults = genericCrawlerService.scrapeWithXPath(profileUrl, reviewSelectors, basicOptions);

            for (Map.Entry<String, String> entry : reviewResults.entrySet()) {
                String value = entry.getValue();
                if (value != null && !value.trim().isEmpty()) {
                    log.info("✅ {} found: {} characters", entry.getKey(), value.length());
                    if (value.length() > 100) {
                        log.info("   Preview: {}", value.substring(0, 100) + "...");
                    }
                } else {
                    log.warn("❌ {} not found", entry.getKey());
                }
            }

            // Test 3: Check for any UL elements
            log.info("Step 3: Testing UL elements...");
            Map<String, String> ulSelectors = new HashMap<>();
            ulSelectors.put("allUls", "//ul");
            ulSelectors.put("strengthWeaknessUls", "//ul[contains(@class, 'strength-weakness-wrapper')]");
            ulSelectors.put("anyClassUls", "//ul[@class]");

            Map<String, String> ulResults = genericCrawlerService.scrapeWithXPath(profileUrl, ulSelectors, basicOptions);

            for (Map.Entry<String, String> entry : ulResults.entrySet()) {
                String value = entry.getValue();
                if (value != null && !value.trim().isEmpty()) {
                    log.info("✅ {} found: {} characters", entry.getKey(), value.length());
                    if (value.length() > 200) {
                        log.info("   Preview: {}", value.substring(0, 200) + "...");
                    } else {
                        log.info("   Full content: {}", value);
                    }
                } else {
                    log.warn("❌ {} not found", entry.getKey());
                }
            }

        } catch (Exception e) {
            log.error("Error during basic testing: {}", e.getMessage(), e);
        }

        log.info("=== END BASIC TESTING ===");
    }
}
